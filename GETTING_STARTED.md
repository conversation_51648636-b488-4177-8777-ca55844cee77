# 🚀 Getting Started with <PERSON><PERSON><PERSON>

## Prerequisites

### 1. Install Ollama
First, install Ollama on your system:

**macOS:**
```bash
brew install ollama
```

**Linux/Windows:**
Visit https://ollama.ai/download and follow the installation instructions.

### 2. Install Required AI Models
Once Ollama is installed, pull the required models:

```bash
# Install the embedding model (for semantic search)
ollama pull nomic-embed-text

# Install the chat model (for generating responses)
ollama pull phi3:mini

# Verify installation
ollama list
```

### 3. Start Ollama Server
Keep this running in a terminal:
```bash
ollama serve
```

## Running MindMesh

### Development Mode
```bash
# Clone the repo
git clone <repository-url>
cd mindmesh-mvp

# Install dependencies
npm install

# Run in development mode
npm run tauri dev
```

### Production Build
```bash
# Build the application
npm run tauri build

# The built app will be in src-tauri/target/release/bundle/
```

## How to Use

### 1. Save Memories
- Type anything you want to remember in the textarea
- Click "Save Memory" 
- Examples:
  - "Meeting with <PERSON> about project proposal on Friday 2pm"
  - "Remember to buy groceries: milk, eggs, bread"
  - "Great restaurant: Blue Table on 5th street, try the pasta"

### 2. Query Your Memories
- Use the "Ask Your Memories" section
- Type natural language questions
- Examples:
  - "What meetings do I have this week?"
  - "What restaurants did I save?"
  - "What did I forget to follow up on?"
  - "What groceries do I need to buy?"

### 3. AI Agent Features
- **Semantic Search**: Finds relevant memories even if you don't use exact words
- **Contextual Responses**: AI provides human-like answers based on your memories
- **Privacy**: Everything runs locally, no data leaves your machine

## Tips for Better Results

### Writing Good Memories
- Be specific with dates and names
- Include context ("meeting with...", "need to...", "remember to...")
- Use consistent naming for people and places

### Asking Good Questions
- Use natural language
- Be specific about what you're looking for
- Try different phrasings if you don't get good results

## Troubleshooting

### "Failed to call Ollama" Error
- Make sure `ollama serve` is running
- Check that Ollama is accessible at http://localhost:11434
- Verify models are installed: `ollama list`

### Slow Performance
- Try smaller models: `ollama pull llama3.2:1b`
- Reduce the number of memories for faster processing
- Consider upgrading your hardware for better performance

### No Relevant Memories Found
- Save more memories with diverse content
- Try rephrasing your question
- Use more specific or more general terms

## Next Steps

Once you're comfortable with the basics:
- Experiment with different types of memories (notes, todos, ideas, facts)
- Try various question styles to see what works best
- Use the AI agent to discover patterns in your saved information

Happy memory management! 🧠✨ 
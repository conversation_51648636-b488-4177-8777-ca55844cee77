<script lang="ts">
  import AgentChat from '$lib/components/AgentChat.svelte';
  import { page } from '$app/stores';
  import { onMount } from 'svelte';
  import { get } from 'svelte/store';

  let agentId = '';
  let agentName = '';
  let loading = true;
  let error: string | null = null;

  onMount(async () => {
    agentId = get(page).params.agent_id;
    try {
      // Safe dynamic import to avoid SSR issues
      const { invoke } = await import('@tauri-apps/api/core');

      // Add type so TypeScript knows what we're accessing
      const agent: { name?: string; enabled?: boolean } = await invoke('get_agent_permissions', {
        agent_id: agentId
      });

      agentName = agent?.name || agentId;

      if (!agent || agent.enabled === false) {
        error = 'This agent is disabled or does not exist.';
      }
    } catch (e: any) {
      error = e?.message || 'Failed to load agent info.';
    } finally {
      loading = false;
    }
  });
</script>

<div class="agent-chat-page">
  {#if loading}
    <div class="loading">Loading agent info...</div>
  {:else if error}
    <div class="error">{error}</div>
  {:else}
    <AgentChat {agentId} {agentName} />
  {/if}
</div>

<style>
  .agent-chat-page {
    min-height: 60vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    padding-top: 2rem;
  }
  .loading {
    color: #3b82f6;
    font-size: 1.1rem;
    margin-top: 2rem;
  }
  .error {
    color: #d32f2f;
    background: #fff0f0;
    border-radius: 0.5rem;
    padding: 0.7rem 1rem;
    margin-top: 2rem;
    font-size: 1rem;
  }
</style>

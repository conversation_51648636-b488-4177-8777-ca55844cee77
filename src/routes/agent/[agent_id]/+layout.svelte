<script lang="ts">
import { page } from '$app/stores';

let agentId = '';
$: agentId = $page.params.agent_id;
</script>

<nav class="agent-tabs">
  <a class="tab" href={`/agent/${agentId}/chat`} aria-current={$page.url.pathname.endsWith('/chat') ? 'page' : undefined}>
    💬 Chat
  </a>
  <!-- Add more tabs here as needed -->
</nav>

<slot />

<style>
.agent-tabs {
  display: flex;
  gap: 1.5rem;
  margin: 2rem auto 1.5rem auto;
  justify-content: center;
}
.tab {
  padding: 0.6rem 1.3rem;
  border-radius: 0.7rem;
  background: #f4f8ff;
  color: #2563eb;
  text-decoration: none;
  font-weight: 500;
  font-size: 1.08rem;
  transition: background 0.18s, color 0.18s;
}
.tab[aria-current='page'], .tab.active {
  background: #2563eb;
  color: #fff;
  font-weight: 600;
}
.tab:hover {
  background: #dbeafe;
  color: #1d4ed8;
}
</style> 
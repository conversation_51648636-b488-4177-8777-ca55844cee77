<script lang="ts">
  import Toast from '../../lib/components/Toast.svelte';
  import { onMount } from 'svelte';
  import { invoke } from '@tauri-apps/api/core';
  import { listen } from '@tauri-apps/api/event';

  let initialized = false;

  async function initializeTauriAPIs() {
    try {
      // Test if we can call a function to confirm <PERSON><PERSON> is available
      await invoke('get_vault_stats');
      console.log("✅ Tauri invoke available");
      initialized = true;
    } catch (e) {
      console.error("❌ Tauri invoke not available:", e);
      initialized = false;
    }

    return initialized;
  }

  interface Integration {
    id: string;
    name: string;
    icon: string;
    description: string;
    connected: boolean;
  }

  let integrations: Integration[] = [
    {
      id: 'notion',
      name: 'Notion',
      icon: '📝',
      description: 'Sync and remember your Notion pages and databases.',
      connected: false,
    },
    {
      id: 'slack',
      name: 'Slack',
      icon: '💬',
      description: 'Let agents live in your Slack workspace and remember conversations.',
      connected: false,
    },
    {
      id: 'gmail',
      name: 'Gmail',
      icon: '📧',
      description: 'Ingest and recall important emails.',
      connected: false,
    },
    {
      id: 'github',
      name: '<PERSON><PERSON><PERSON><PERSON>',
      icon: '🐙',
      description: 'Let agents help you with code and issues.',
      connected: false,
    },
    // Add more as needed
  ];

  // Toast state
  let toastMsg = "";
  let toastType: 'info' | 'success' | 'error' = 'info';
  let showToast = false;

  // Helper function to wait for Tauri to be ready
  async function waitForTauri(): Promise<boolean> {
    return await initializeTauriAPIs();
  }

  async function connectIntegration(id: string) {
    if (id === 'notion') {
      console.log('🚀 Attempting Notion connection...');

      // Check if Tauri is available
      const tauriReady = await waitForTauri();

      if (!tauriReady) {
        console.log('❌ Tauri APIs not available');
        toastMsg = 'Tauri APIs not available. Please make sure you are running the desktop app.';
        toastType = 'error';
        showToast = true;
        return;
      }

      console.log('✅ Tauri APIs are ready!');

      // Generate Notion OAuth URL
      try {
        console.log('🔗 Generating Notion OAuth URL...');
        const url = await invoke('notion_generate_oauth_url') as string;
        console.log('✅ Successfully generated OAuth URL:', url);

        // Open the URL
        try {
          await invoke('open_url', { url });
          console.log('✅ Opened URL with Tauri command');
        } catch (e) {
          console.log('⚠️ Tauri open command failed, using window.open:', e);
          window.open(url, '_blank');
        }

        toastMsg = "Check your browser to complete Notion connection...";
        toastType = "info";
        showToast = true;

      } catch (e) {
        console.error('❌ Failed to invoke Tauri command:', e);
        toastMsg = `Failed to start Notion connection: ${e}`;
        toastType = 'error';
        showToast = true;
      }
    } else {
      toastMsg = 'Integration coming soon!';
      toastType = 'info';
      showToast = true;
    }
  }

  function disconnectIntegration(id: string) {
    toastMsg = 'Disconnecting ' + id + ' (not implemented)';
    toastType = 'info';
    showToast = true;
  }

  onMount(() => {
    (async () => {
      const tauriReady = await initializeTauriAPIs();

      if (!tauriReady) {
        console.log("❌ Tauri not available");
        return;
      }

      listen('tauri://uri', async (event: any) => {
        const url = event.payload as string;
        if (url.startsWith('mindmesh://notion-auth')) {
          const params = new URL(url).searchParams;
          const code = params.get('code');
          const state = params.get('state');
          if (code && state) {
            try {
              await invoke('handle_oauth_callback', { code, state });
              toastMsg = 'Notion connected!';
              toastType = 'success';
              showToast = true;
            } catch (e) {
              toastMsg = 'Failed to connect Notion: ' + e;
              toastType = 'error';
              showToast = true;
            }
          }
        }
      });
    })();
  });
</script>

<Toast message={toastMsg} type={toastType} bind:show={showToast} />

<div class="max-w-3xl mx-auto p-8">
  <h1 class="text-4xl font-bold mb-4 text-white">Web Integrations</h1>
  <p class="mb-8 text-purple-200">
    Connect your favorite apps to MindMesh. Agents can live in these apps, remember and retrieve information, and help you wherever you work. All data is stored locally and privately—nothing leaves your device.
  </p>
  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    {#each integrations as integration}
      <div class="bg-white/5 border border-white/10 rounded-xl p-6 flex flex-col gap-3">
        <div class="flex items-center gap-3 mb-2">
          <span class="text-2xl">{integration.icon}</span>
          <span class="font-semibold text-lg text-white">{integration.name}</span>
        </div>
        <div class="text-purple-200 text-sm mb-2">{integration.description}</div>
        <div class="flex gap-2 mt-auto">
          {#if integration.connected}
            <button class="secondaryBtn flex-1" on:click={() => disconnectIntegration(integration.id)}>Disconnect</button>
            <span class="text-green-400 text-xs">Connected</span>
          {:else}
            <button class="primaryBtn flex-1" on:click={() => connectIntegration(integration.id)}>Connect</button>
          {/if}
        </div>
      </div>
    {/each}
  </div>
</div>

<style>
body { background: #181028; }
.primaryBtn {
  background: linear-gradient(to right,#8b5cf6,#ec4899);
  padding: .75rem 2rem;
  border-radius: .75rem;
  color: white;
  font-weight: bold;
  border: none;
  cursor: pointer;
}
.secondaryBtn {
  background: rgba(255,255,255,0.1);
  padding: .5rem 1rem;
  border: 1px solid rgba(255,255,255,0.15);
  border-radius: .5rem;
  color: white;
  cursor: pointer;
}
</style> 
<script lang="ts">
  import Toast from '../../lib/components/Toast.svelte';
  import { onMount } from 'svelte';

  // Try multiple import methods for Tauri APIs
  let invoke: any;
  let listen: any;

  // Initialize Tauri APIs
  async function initializeTauriAPIs() {
    console.log('🔍 Initializing Tauri APIs...');
    console.log('🌐 Window object available:', typeof window !== 'undefined');

    if (typeof window !== 'undefined') {
      console.log('🔍 Available globals:', Object.keys(window).filter(k => k.includes('TAURI') || k.includes('tauri')));
      console.log('🔍 __TAURI_INTERNALS__ exists:', '__TAURI_INTERNALS__' in window);
      console.log('🔍 __TAURI_INTERNALS__ type:', typeof (window as any).__TAURI_INTERNALS__);

      if ((window as any).__TAURI_INTERNALS__) {
        console.log('🔍 __TAURI_INTERNALS__ keys:', Object.keys((window as any).__TAURI_INTERNALS__));
        console.log('🔍 __TAURI_INTERNALS__.invoke exists:', 'invoke' in (window as any).__TAURI_INTERNALS__);
        console.log('🔍 __TAURI_INTERNALS__.invoke type:', typeof (window as any).__TAURI_INTERNALS__.invoke);
      }

      console.log('🔍 __TAURI__ exists:', '__TAURI__' in window);
      console.log('🔍 __TAURI__ type:', typeof (window as any).__TAURI__);
    }

    // Method 1: Try accessing global __TAURI_INTERNALS__ (most reliable in production)
    if (typeof window !== 'undefined' && (window as any).__TAURI_INTERNALS__ && (window as any).__TAURI_INTERNALS__.invoke) {
      invoke = (window as any).__TAURI_INTERNALS__.invoke;
      console.log('✅ Loaded Tauri APIs from __TAURI_INTERNALS__');

      // Try to get listen function too
      try {
        const tauriEvent = await import('@tauri-apps/api/event');
        listen = tauriEvent.listen;
        console.log('✅ Loaded listen from @tauri-apps/api/event');
      } catch (e) {
        console.log('⚠️ Could not load listen function:', e);
      }
      return true;
    }

    // Method 2: Try accessing global __TAURI__ object
    if (typeof window !== 'undefined' && (window as any).__TAURI__ && (window as any).__TAURI__.invoke) {
      invoke = (window as any).__TAURI__.invoke;
      listen = (window as any).__TAURI__.event?.listen;
      console.log('✅ Loaded Tauri APIs from __TAURI__ global object');
      return true;
    }

    // Method 3: Try Tauri v2 import (fallback)
    try {
      const tauriCore = await import('@tauri-apps/api/core');
      const tauriEvent = await import('@tauri-apps/api/event');
      invoke = tauriCore.invoke;
      listen = tauriEvent.listen;
      console.log('✅ Loaded Tauri v2 APIs via import');
      return true;
    } catch (e) {
      console.log('❌ Failed to load Tauri v2 APIs via import:', e);
    }

    console.log('❌ No Tauri APIs available');
    return false;
  }

  interface Integration {
    id: string;
    name: string;
    icon: string;
    description: string;
    connected: boolean;
  }

  let integrations: Integration[] = [
    {
      id: 'notion',
      name: 'Notion',
      icon: '📝',
      description: 'Sync and remember your Notion pages and databases.',
      connected: false,
    },
    {
      id: 'slack',
      name: 'Slack',
      icon: '💬',
      description: 'Let agents live in your Slack workspace and remember conversations.',
      connected: false,
    },
    {
      id: 'gmail',
      name: 'Gmail',
      icon: '📧',
      description: 'Ingest and recall important emails.',
      connected: false,
    },
    {
      id: 'github',
      name: 'GitHub',
      icon: '🐙',
      description: 'Let agents help you with code and issues.',
      connected: false,
    },
    // Add more as needed
  ];

  // Toast state
  let toastMsg = "";
  let toastType: 'info' | 'success' | 'error' = 'info';
  let showToast = false;

  // Helper function to wait for Tauri to be ready
  async function waitForTauri(maxAttempts = 10, delay = 100): Promise<boolean> {
    // First try to initialize the APIs
    const initialized = await initializeTauriAPIs();
    if (!initialized) {
      console.log('Failed to initialize Tauri APIs');
      return false;
    }

    for (let i = 0; i < maxAttempts; i++) {
      try {
        // Try to invoke a command we know exists
        await invoke('get_vault_stats');
        console.log('Tauri APIs detected successfully!');
        return true;
      } catch (e) {
        console.log(`Attempt ${i + 1}/${maxAttempts} failed:`, e);
        if (i === maxAttempts - 1) {
          console.log('Tauri not available after', maxAttempts, 'attempts');
          console.log('Final error:', e);
          return false;
        }
        // Wait before trying again
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    return false;
  }

  async function connectIntegration(id: string) {
    if (id === 'notion') {
      console.log('🚀 Attempting Notion connection...');
      console.log('🌐 Window location:', typeof window !== 'undefined' ? window.location.href : 'undefined');

      // DIRECT APPROACH: Skip all detection and use __TAURI_INTERNALS__ directly
      if (typeof window !== 'undefined' && (window as any).__TAURI_INTERNALS__ && (window as any).__TAURI_INTERNALS__.invoke) {
        console.log('🎯 Using direct __TAURI_INTERNALS__.invoke approach');

        try {
          const directInvoke = (window as any).__TAURI_INTERNALS__.invoke;
          console.log('🔗 Generating Notion OAuth URL directly...');

          const url = await directInvoke('notion_generate_oauth_url');
          console.log('✅ Successfully generated OAuth URL:', url);

          // Try to open the URL
          try {
            await directInvoke('open_url', { url });
            console.log('✅ Opened URL with Tauri command');
          } catch (e) {
            console.log('⚠️ Tauri open command failed, using window.open:', e);
            window.open(url, '_blank');
          }

          toastMsg = "Check your browser to complete Notion connection...";
          toastType = "info";
          showToast = true;
          return;

        } catch (e) {
          console.error('❌ Direct invoke failed:', e);
          toastMsg = `Failed to start Notion connection: ${e}`;
          toastType = 'error';
          showToast = true;
          return;
        }
      }

      // FALLBACK: Original detection logic
      console.log('⚠️ __TAURI_INTERNALS__ not available, trying fallback...');
      console.log('🔍 Invoke function type:', typeof invoke);

      // Wait for Tauri to be ready
      console.log('⏳ Waiting for Tauri APIs to be ready...');
      const tauriReady = await waitForTauri();

      if (!tauriReady) {
        console.log('❌ Tauri APIs not available');
        toastMsg = 'Tauri APIs not available. Please make sure you are running the desktop app.';
        toastType = 'error';
        showToast = true;
        return;
      }

      console.log('✅ Tauri APIs are ready!');

      // Try to directly invoke the Notion OAuth command
      try {
        console.log('🔗 Attempting to generate Notion OAuth URL...');
        const url = await invoke('notion_generate_oauth_url') as string;
        console.log('✅ Successfully generated OAuth URL:', url);

        // Try to open the URL
        try {
          await invoke('open_url', { url });
          console.log('✅ Opened URL with Tauri command');
        } catch (e) {
          console.log('⚠️ Tauri open command failed, using window.open:', e);
          window.open(url, '_blank');
        }

        toastMsg = "Check your browser to complete Notion connection...";
        toastType = "info";
        showToast = true;

      } catch (e) {
        console.error('❌ Failed to invoke Tauri command:', e);
        toastMsg = `Failed to start Notion connection: ${e}`;
        toastType = 'error';
        showToast = true;
      }
    } else {
      toastMsg = 'Integration coming soon!';
      toastType = 'info';
      showToast = true;
    }
  }

  function disconnectIntegration(id: string) {
    toastMsg = 'Disconnecting ' + id + ' (not implemented)';
    toastType = 'info';
    showToast = true;
  }

  onMount(() => {
    (async () => {
      // Initialize Tauri APIs first
      const initialized = await initializeTauriAPIs();

      if (initialized) {
        console.log('Tauri APIs initialized, setting up OAuth callback listener');
        try {
          listen('tauri://uri', async (event: any) => {
            const url = event.payload as string;
            if (url.startsWith('mindmesh://notion-auth')) {
              const params = new URL(url).searchParams;
              const code = params.get('code');
              const state = params.get('state');
              if (code && state) {
                try {
                  await invoke('handle_oauth_callback', { code, state });
                  toastMsg = 'Notion connected!';
                  toastType = 'success';
                  showToast = true;
                } catch (e) {
                  toastMsg = 'Failed to connect Notion: ' + e;
                  toastType = 'error';
                  showToast = true;
                }
              }
            }
          });
        } catch (e) {
          console.log('Failed to set up OAuth callback listener:', e);
        }
      } else {
        console.log('Tauri APIs not available, skipping OAuth callback setup');
      }
    })();
  });
</script>

<Toast message={toastMsg} type={toastType} bind:show={showToast} />

<div class="max-w-3xl mx-auto p-8">
  <h1 class="text-4xl font-bold mb-4 text-white">Web Integrations</h1>
  <p class="mb-8 text-purple-200">
    Connect your favorite apps to MindMesh. Agents can live in these apps, remember and retrieve information, and help you wherever you work. All data is stored locally and privately—nothing leaves your device.
  </p>
  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    {#each integrations as integration}
      <div class="bg-white/5 border border-white/10 rounded-xl p-6 flex flex-col gap-3">
        <div class="flex items-center gap-3 mb-2">
          <span class="text-2xl">{integration.icon}</span>
          <span class="font-semibold text-lg text-white">{integration.name}</span>
        </div>
        <div class="text-purple-200 text-sm mb-2">{integration.description}</div>
        <div class="flex gap-2 mt-auto">
          {#if integration.connected}
            <button class="secondaryBtn flex-1" on:click={() => disconnectIntegration(integration.id)}>Disconnect</button>
            <span class="text-green-400 text-xs">Connected</span>
          {:else}
            <button class="primaryBtn flex-1" on:click={() => connectIntegration(integration.id)}>Connect</button>
          {/if}
        </div>
      </div>
    {/each}
  </div>
</div>

<style>
body { background: #181028; }
.primaryBtn {
  background: linear-gradient(to right,#8b5cf6,#ec4899);
  padding: .75rem 2rem;
  border-radius: .75rem;
  color: white;
  font-weight: bold;
  border: none;
  cursor: pointer;
}
.secondaryBtn {
  background: rgba(255,255,255,0.1);
  padding: .5rem 1rem;
  border: 1px solid rgba(255,255,255,0.15);
  border-radius: .5rem;
  color: white;
  cursor: pointer;
}
</style> 
// MindMesh TypeScript interfaces

export interface MemoryEntry {
  content: string;
  timestamp: string;
  author: string;
  source: string;
  id: string;
  tags: string[];
}

export interface VaultStats {
  total_entries: number;
  vault_version: string;
  tags_count: number;
  sources: Record<string, number>;
}

// ========== AGENT API TYPES ==========

export interface AgentPermissions {
  agent_id: string;
  name: string;
  description?: string;
  can_read_sources: string[];     // ["slack", "git", "manual"]
  can_read_tags: string[];        // ["urgent", "client:enterprise"] 
  can_write_tags: string[];       // ["onboarding", "agent-created"]
  can_read_authors: string[];     // ["slack:*", "git:jane.smith"]
  max_memory_age_days?: number;   // Limit to recent memories
  enabled: boolean;
  created_at: string;
  updated_at: string;
}

export interface AgentQueryRequest {
  agent_id: string;
  question: string;
  max_results?: number;
  min_similarity?: number;
}

export interface AgentQueryResponse {
  agent_id: string;
  question: string;
  response: string;
  memories_used: PermissionedMemorySnippet[];
  total_memories_found: number;
  query_timestamp: string;
}

export interface PermissionedMemorySnippet {
  id: string;
  content: string;
  timestamp: string;
  author: string;
  source: string;
  tags: string[];
  similarity_score: number;
  provenance: MemoryProvenance;
}

export interface MemoryProvenance {
  source_type: string;
  integration_id?: string;
  source_metadata: Record<string, any>;
  permissions_used: string; // Which agent permission allowed this access
}

// Ingestion API types
export interface SlackIngestionRequest {
  text: string;
  channel: string;
  author: string;
  timestamp?: string;
  thread_ts?: string;
  message_type?: string;
}

export interface SearchRequest {
  source_filter?: string;
  tag_filter?: string[];
  author_filter?: string;
  start_date?: string;
  end_date?: string;
} 
<script lang="ts">
  export let message = "";
  export let type: 'info' | 'success' | 'error' = 'info';
  export let show = false;
  export let duration = 3000;
  let timeout: ReturnType<typeof setTimeout>;

  $: if (show && message) {
    clearTimeout(timeout);
    timeout = setTimeout(() => show = false, duration);
  }
</script>

{#if show && message}
  <div class="toast {type}">
    {message}
  </div>
{/if}

<style>
.toast {
  position: fixed;
  left: 50%;
  bottom: 2rem;
  transform: translateX(-50%);
  min-width: 220px;
  max-width: 90vw;
  background: #222;
  color: #fff;
  padding: 1em 2em;
  border-radius: 0.5em;
  box-shadow: 0 2px 16px rgba(0,0,0,0.18);
  z-index: 9999;
  font-size: 1.1em;
  opacity: 0.97;
  display: flex;
  align-items: center;
  gap: 0.7em;
  animation: toast-in 0.2s;
}
.toast.success { background: #22c55e; }
.toast.error { background: #ef4444; }
.toast.info { background: #6366f1; }
@keyframes toast-in {
  from { opacity: 0; transform: translateX(-50%) translateY(30px); }
  to { opacity: 0.97; transform: translateX(-50%) translateY(0); }
}
</style> 
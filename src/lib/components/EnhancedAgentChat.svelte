<script lang="ts">
  import { invoke } from '@tauri-apps/api/core';
  import { onMount } from 'svelte';

  export let agentId: string;
  export let agentName: string;

  interface ChatMessage {
    role: 'user' | 'agent';
    content: string;
    timestamp: Date;
    suggestions?: string[];
  }

  interface AppIntegration {
    id: string;
    app_type: any;
    name: string;
    connection_status: any;
  }

  let messages: ChatMessage[] = [];
  let currentMessage = '';
  let isLoading = false;
  let availableApps: AppIntegration[] = [];
  let showPermissionRequest = false;
  let permissionRequestForm = {
    app_type: '',
    permissions: ['read'],
    justification: ''
  };

  const commonPermissions = {
    notion: ['read', 'write', 'create_pages', 'read_database'],
    gmail: ['read', 'send', 'modify_labels'],
    calendar: ['read', 'write', 'create_events'],
    slack: ['read', 'write', 'channels:read', 'channels:history'],
    github: ['read', 'write', 'repo', 'issues'],
    linear: ['read', 'write', 'create_issues']
  };

  onMount(async () => {
    await loadAvailableApps();
    // Add welcome message
    messages = [{
      role: 'agent',
      content: `Hi! I'm ${agentName}. I can help you with information from your connected apps. Ask me anything!`,
      timestamp: new Date()
    }];
  });

  async function loadAvailableApps() {
    try {
      availableApps = await invoke('get_app_integrations');
    } catch (error) {
      console.error('Failed to load apps:', error);
    }
  }

  async function sendMessage() {
    if (!currentMessage.trim() || isLoading) return;

    const userMessage: ChatMessage = {
      role: 'user',
      content: currentMessage,
      timestamp: new Date()
    };

    messages = [...messages, userMessage];
    const question = currentMessage;
    currentMessage = '';
    isLoading = true;

    try {
      const response = await invoke('enhanced_agent_query', {
        agentId,
        question
      });

      const agentMessage: ChatMessage = {
        role: 'agent',
        content: response.response,
        timestamp: new Date()
      };

      // Check if the response contains suggestions for app permissions
      if (response.response.includes('💡 Suggestions:')) {
        const parts = response.response.split('💡 Suggestions:');
        agentMessage.content = parts[0].trim();
        agentMessage.suggestions = parts[1].split('\n').filter(s => s.trim()).map(s => s.trim());
      }

      messages = [...messages, agentMessage];
    } catch (error) {
      console.error('Failed to query agent:', error);
      messages = [...messages, {
        role: 'agent',
        content: 'Sorry, I encountered an error while processing your request.',
        timestamp: new Date()
      }];
    } finally {
      isLoading = false;
    }
  }

  async function requestAppPermission() {
    if (!permissionRequestForm.app_type || !permissionRequestForm.justification.trim()) {
      alert('Please fill in all fields');
      return;
    }

    try {
      await invoke('agent_request_app_permission', {
        agentId,
        appType: permissionRequestForm.app_type,
        permissions: permissionRequestForm.permissions,
        justification: permissionRequestForm.justification
      });

      showPermissionRequest = false;
      permissionRequestForm = { app_type: '', permissions: ['read'], justification: '' };

      // Add confirmation message
      messages = [...messages, {
        role: 'agent',
        content: '✅ I\'ve requested permission to access that app. You\'ll receive a notification to approve or deny the request.',
        timestamp: new Date()
      }];
    } catch (error) {
      console.error('Failed to request permission:', error);
      alert('Failed to request permission: ' + error);
    }
  }

  function getAppTypeInfo(appType: any) {
    const typeStr = typeof appType === 'string' ? appType : 
                   appType.Custom ? appType.Custom : 
                   Object.keys(appType)[0]?.toLowerCase();
    
    const appTypes = [
      { value: 'notion', label: 'Notion', icon: '📝' },
      { value: 'gmail', label: 'Gmail', icon: '📧' },
      { value: 'calendar', label: 'Google Calendar', icon: '📅' },
      { value: 'slack', label: 'Slack', icon: '💬' },
      { value: 'github', label: 'GitHub', icon: '🐙' },
      { value: 'linear', label: 'Linear', icon: '📋' }
    ];
    
    return appTypes.find(t => t.value === typeStr) || { value: typeStr, label: typeStr, icon: '🔗' };
  }

  function isConnected(app: AppIntegration) {
    return typeof app.connection_status === 'string' && app.connection_status === 'Connected' ||
           Object.keys(app.connection_status)[0] === 'Connected';
  }

  function formatTime(date: Date) {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }

  $: connectedApps = availableApps.filter(isConnected);
  $: availablePermissions = permissionRequestForm.app_type ? 
    (commonPermissions[permissionRequestForm.app_type as keyof typeof commonPermissions] || ['read']) : 
    ['read'];
</script>

<div class="bg-white/5 backdrop-blur-lg border border-white/10 rounded-xl p-6">
  <div class="flex items-center justify-between mb-4">
    <h3 class="text-xl font-semibold text-white">💬 Chat with {agentName}</h3>
    <button 
      on:click={() => showPermissionRequest = true}
      class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 transition-colors"
    >
      Request App Access
    </button>
  </div>

  <!-- Chat Messages -->
  <div class="h-96 overflow-y-auto mb-4 space-y-3 bg-black/20 rounded-lg p-4">
    {#each messages as message}
      <div class="flex {message.role === 'user' ? 'justify-end' : 'justify-start'}">
        <div class="max-w-[80%] {message.role === 'user' ? 'bg-blue-600' : 'bg-gray-700'} text-white rounded-lg p-3">
          <div class="text-sm mb-1">
            <strong>{message.role === 'user' ? 'You' : agentName}</strong>
            <span class="text-xs opacity-70 ml-2">{formatTime(message.timestamp)}</span>
          </div>
          <div class="whitespace-pre-wrap">{message.content}</div>
          
          {#if message.suggestions}
            <div class="mt-3 space-y-2">
              {#each message.suggestions as suggestion}
                <div class="bg-yellow-600/20 border border-yellow-500/30 rounded p-2 text-sm">
                  {suggestion}
                  {#if suggestion.includes('request access')}
                    <button 
                      on:click={() => showPermissionRequest = true}
                      class="ml-2 bg-yellow-600 text-white px-2 py-1 rounded text-xs hover:bg-yellow-700"
                    >
                      Request Now
                    </button>
                  {/if}
                </div>
              {/each}
            </div>
          {/if}
        </div>
      </div>
    {/each}
    
    {#if isLoading}
      <div class="flex justify-start">
        <div class="bg-gray-700 text-white rounded-lg p-3">
          <div class="flex items-center gap-2">
            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            <span>{agentName} is thinking...</span>
          </div>
        </div>
      </div>
    {/if}
  </div>

  <!-- Message Input -->
  <div class="flex gap-2">
    <input 
      type="text" 
      bind:value={currentMessage}
      on:keydown={(e) => e.key === 'Enter' && sendMessage()}
      placeholder="Ask me anything..."
      class="flex-1 bg-white/10 border border-white/20 rounded-lg px-4 py-2 text-white placeholder-gray-400"
      disabled={isLoading}
    />
    <button 
      on:click={sendMessage}
      disabled={isLoading || !currentMessage.trim()}
      class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
    >
      Send
    </button>
  </div>
</div>

<!-- Permission Request Modal -->
{#if showPermissionRequest}
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 w-full max-w-md">
      <h2 class="text-xl font-semibold mb-4">Request App Permission</h2>
      
      <form on:submit|preventDefault={requestAppPermission} class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">App</label>
          <select bind:value={permissionRequestForm.app_type} class="w-full border border-gray-300 rounded-lg px-3 py-2" required>
            <option value="">Select an app</option>
            {#each connectedApps as app}
              {@const appInfo = getAppTypeInfo(app.app_type)}
              <option value={appInfo.value}>{appInfo.icon} {app.name}</option>
            {/each}
          </select>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Permissions</label>
          <div class="space-y-2 max-h-32 overflow-y-auto">
            {#each availablePermissions as permission}
              <label class="flex items-center">
                <input 
                  type="checkbox" 
                  bind:group={permissionRequestForm.permissions}
                  value={permission}
                  class="mr-2"
                />
                <span class="text-sm">{permission}</span>
              </label>
            {/each}
          </div>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Justification</label>
          <textarea 
            bind:value={permissionRequestForm.justification}
            placeholder="Explain why you need access to this app..."
            class="w-full border border-gray-300 rounded-lg px-3 py-2 h-24"
            required
          ></textarea>
        </div>
        
        <div class="flex gap-3 pt-4">
          <button 
            type="button"
            on:click={() => showPermissionRequest = false}
            class="flex-1 border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50"
          >
            Cancel
          </button>
          <button 
            type="submit"
            class="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
          >
            Request Permission
          </button>
        </div>
      </form>
    </div>
  </div>
{/if}

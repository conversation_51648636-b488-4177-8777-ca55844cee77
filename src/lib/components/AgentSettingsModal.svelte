<script lang="ts">
  import { createEventDispatcher, onMount } from 'svelte';
  import type { AgentPermissions } from '../../lib/types';

  export let agent: AgentPermissions;
  export let allSources: string[] = [];
  export let allTags: string[] = [];
  export let onSave: (updated: AgentPermissions) => void;
  export let onClose: () => void;

  const dispatch = createEventDispatcher();

  let editable: AgentPermissions;
  let authorInput = '';

  onMount(() => {
    editable = { ...agent };
    authorInput = editable.can_read_authors.join(', ');
  });

  function toggleSource(src: string) {
    if (editable.can_read_sources.includes(src)) {
      editable.can_read_sources = editable.can_read_sources.filter(s => s !== src);
    } else {
      editable.can_read_sources = [...editable.can_read_sources, src];
    }
  }

  function toggleTag(tag: string) {
    if (editable.can_read_tags.includes(tag)) {
      editable.can_read_tags = editable.can_read_tags.filter(t => t !== tag);
    } else {
      editable.can_read_tags = [...editable.can_read_tags, tag];
    }
  }

  function save() {
    editable.can_read_authors = authorInput.split(',').map(a => a.trim()).filter(Boolean);
    editable.updated_at = new Date().toISOString();
    onSave(editable);
  }
</script>

<div class="modal-backdrop" on:click={onClose}></div>
<div class="modal">
  <h2 class="modal-title">Agent Settings: {agent.name}</h2>
  <div class="modal-section">
    <label class="modal-label">
      <input type="checkbox" bind:checked={editable.enabled} /> Enabled
    </label>
  </div>
  <div class="modal-section">
    <div class="modal-label">Sources</div>
    <div class="flex flex-wrap gap-2">
      {#each allSources as src}
        <label class="modal-checkbox">
          <input type="checkbox" checked={editable.can_read_sources.includes(src)} on:change={() => toggleSource(src)} /> {src}
        </label>
      {/each}
    </div>
  </div>
  <div class="modal-section">
    <div class="modal-label">Tags</div>
    <div class="flex flex-wrap gap-2">
      {#each allTags as tag}
        <label class="modal-checkbox">
          <input type="checkbox" checked={editable.can_read_tags.includes(tag)} on:change={() => toggleTag(tag)} /> #{tag}
        </label>
      {/each}
    </div>
  </div>
  <div class="modal-section">
    <label class="modal-label">Authors (comma-separated)</label>
    <input class="modal-input" type="text" bind:value={authorInput} />
  </div>
  <div class="modal-section">
    <label class="modal-label">Max Memory Age (days)</label>
    <input class="modal-input" type="number" min="1" bind:value={editable.max_memory_age_days} />
  </div>
  <div class="modal-actions">
    <button class="primaryBtn" on:click={save}>Save</button>
    <button class="secondaryBtn" on:click={onClose}>Cancel</button>
  </div>
</div>

<style>
.modal-backdrop {
  position: fixed;
  inset: 0;
  background: rgba(0,0,0,0.5);
  z-index: 1000;
}
.modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #181028;
  color: #fff;
  border-radius: 1rem;
  box-shadow: 0 8px 32px rgba(0,0,0,0.25);
  padding: 2rem;
  min-width: 340px;
  max-width: 95vw;
  z-index: 1001;
}
.modal-title {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1.2rem;
}
.modal-section {
  margin-bottom: 1.1rem;
}
.modal-label {
  font-weight: 500;
  margin-bottom: 0.4rem;
  display: block;
}
.modal-checkbox {
  margin-right: 1.2em;
  font-size: 1em;
}
.modal-input {
  width: 100%;
  padding: 0.6em 1em;
  border-radius: 0.5em;
  border: 1px solid #a78bfa;
  background: #22143a;
  color: #fff;
  margin-top: 0.2em;
}
.modal-actions {
  display: flex;
  gap: 1em;
  justify-content: flex-end;
  margin-top: 1.5em;
}
</style> 
<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  const dispatch = createEventDispatcher();

  export let show = false;

  function handleContinue() {
    dispatch('continue');
  }
  function handleClose() {
    dispatch('close');
  }
</script>

{#if show}
  <div class="modal-backdrop" on:click={handleClose}></div>
  <div class="modal">
    <h2 class="modal-title">Connect to Notion</h2>
    <div class="modal-section">
      <div class="mb-4 text-purple-200">
        MindMesh will connect to your Notion workspace to securely access and sync selected pages and databases. <br/>
        <b>What will be accessed:</b>
      </div>
      <ul class="list-disc ml-6 mt-2 text-purple-200">
        <li>Read-only access to pages and databases you select</li>
        <li>Page content, titles, and metadata</li>
        <li>No data is sent to any external server; everything is stored locally and encrypted</li>
      </ul>
      <span class="block mt-3 text-xs text-purple-300">You can disconnect at any time. MindMesh will never share your data without your explicit consent.</span>
      <button class="primaryBtn w-full mt-6" on:click={handleContinue}>Continue with Notion</button>
      <button class="secondaryBtn w-full mt-2" on:click={handleClose}>Cancel</button>
    </div>
  </div>
{/if}

<style>
.modal-backdrop {
  position: fixed;
  inset: 0;
  background: rgba(0,0,0,0.5);
  z-index: 1000;
}
.modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #181028;
  color: #fff;
  border-radius: 1rem;
  box-shadow: 0 8px 32px rgba(0,0,0,0.25);
  padding: 2rem;
  min-width: 340px;
  max-width: 95vw;
  z-index: 1001;
}
.modal-title {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1.2rem;
}
.modal-section {
  margin-bottom: 1.1rem;
}
.primaryBtn {
  background: linear-gradient(to right,#8b5cf6,#ec4899);
  padding: .75rem 2rem;
  border-radius: .75rem;
  color: white;
  font-weight: bold;
  border: none;
  cursor: pointer;
}
.secondaryBtn {
  background: rgba(255,255,255,0.1);
  padding: .5rem 1rem;
  border: 1px solid rgba(255,255,255,0.15);
  border-radius: .5rem;
  color: white;
  cursor: pointer;
}
</style> 
<script lang="ts">
  export let traceData: any = null;
  export let isVisible = false;

  interface TraceStep {
    step: number;
    type: 'search' | 'filter' | 'rank' | 'response';
    description: string;
    details: any;
    timestamp: string;
  }

  let mockTraceData: TraceStep[] = [
    {
      step: 1,
      type: 'search',
      description: 'Vector similarity search executed',
      details: {
        query: "What did I learn about React?",
        matches_found: 24,
        similarity_threshold: 0.75
      },
      timestamp: new Date().toISOString()
    },
    {
      step: 2,
      type: 'filter',
      description: 'Applied contextual filters',
      details: {
        date_range: "last 30 days",
        sources_filtered: ["manual", "documentation"],
        tags_considered: ["react", "learning", "frontend"]
      },
      timestamp: new Date().toISOString()
    },
    {
      step: 3,
      type: 'rank',
      description: 'Ranked results by relevance',
      details: {
        ranking_factors: ["semantic_similarity", "recency", "source_credibility"],
        top_memories: 5,
        confidence_score: 0.89
      },
      timestamp: new Date().toISOString()
    },
    {
      step: 4,
      type: 'response',
      description: 'Generated contextual response',
      details: {
        memory_sources_used: 3,
        response_confidence: 0.92,
        synthesis_method: "extractive_summarization"
      },
      timestamp: new Date().toISOString()
    }
  ];

  function getStepIcon(type: string): string {
    switch (type) {
      case 'search': return '🔍';
      case 'filter': return '🔬';
      case 'rank': return '📊';
      case 'response': return '💡';
      default: return '📋';
    }
  }

  function getStepColor(type: string): string {
    switch (type) {
      case 'search': return 'from-blue-500/20 to-cyan-500/20 border-blue-400/30';
      case 'filter': return 'from-purple-500/20 to-indigo-500/20 border-purple-400/30';
      case 'rank': return 'from-orange-500/20 to-red-500/20 border-orange-400/30';
      case 'response': return 'from-green-500/20 to-emerald-500/20 border-green-400/30';
      default: return 'from-gray-500/20 to-slate-500/20 border-gray-400/30';
    }
  }

  function formatTimestamp(timestamp: string): string {
    return new Date(timestamp).toLocaleTimeString();
  }
</script>

{#if isVisible}
  <div class="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
    <div class="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
      <!-- Header -->
      <div class="p-6 border-b border-white/20">
        <div class="flex justify-between items-center">
          <div>
            <h2 class="text-2xl font-bold text-white flex items-center gap-3">
              🔍 <span class="bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent">Memory Trace</span>
            </h2>
            <p class="text-purple-200 mt-2">See how your memory query was processed step by step</p>
          </div>
          <button
            on:click={() => isVisible = false}
            class="text-white/70 hover:text-white hover:bg-white/10 p-2 rounded-lg transition-all duration-300"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>

      <!-- Trace Steps -->
      <div class="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
        <div class="space-y-6">
          {#each mockTraceData as step, index}
            <div class="relative">
              <!-- Timeline connector -->
              {#if index < mockTraceData.length - 1}
                <div class="absolute left-6 top-16 w-0.5 h-full bg-gradient-to-b from-purple-400/50 to-transparent"></div>
              {/if}

              <!-- Step card -->
              <div class="bg-gradient-to-r {getStepColor(step.type)} backdrop-blur-sm rounded-xl border p-6 hover:bg-white/10 transition-all duration-300">
                <div class="flex items-start gap-4">
                  <!-- Step indicator -->
                  <div class="flex-shrink-0 w-12 h-12 bg-white/10 rounded-full flex items-center justify-center text-xl font-bold text-white relative z-10">
                    {getStepIcon(step.type)}
                  </div>

                  <!-- Step content -->
                  <div class="flex-1">
                    <div class="flex justify-between items-start mb-3">
                      <div>
                        <h3 class="text-lg font-bold text-white">Step {step.step}: {step.description}</h3>
                        <p class="text-sm text-purple-200">Type: {step.type}</p>
                      </div>
                      <span class="text-xs text-purple-300 bg-white/10 px-2 py-1 rounded-full">
                        {formatTimestamp(step.timestamp)}
                      </span>
                    </div>

                    <!-- Step details -->
                    <div class="bg-white/5 rounded-lg p-4">
                      {#if step.type === 'search'}
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                          <div class="bg-white/5 p-3 rounded-lg">
                            <div class="text-blue-300 font-medium">Query</div>
                            <div class="text-white mt-1">"{step.details.query}"</div>
                          </div>
                          <div class="bg-white/5 p-3 rounded-lg">
                            <div class="text-blue-300 font-medium">Matches Found</div>
                            <div class="text-white mt-1 text-lg font-bold">{step.details.matches_found}</div>
                          </div>
                          <div class="bg-white/5 p-3 rounded-lg">
                            <div class="text-blue-300 font-medium">Similarity Threshold</div>
                            <div class="text-white mt-1 text-lg font-bold">{(step.details.similarity_threshold * 100).toFixed(0)}%</div>
                          </div>
                        </div>
                      {:else if step.type === 'filter'}
                        <div class="space-y-3 text-sm">
                          <div class="bg-white/5 p-3 rounded-lg">
                            <div class="text-purple-300 font-medium mb-2">Applied Filters</div>
                            <div class="flex flex-wrap gap-2">
                              <span class="bg-purple-500/20 text-purple-200 px-2 py-1 rounded-full text-xs">
                                📅 {step.details.date_range}
                              </span>
                              {#each step.details.sources_filtered as source}
                                <span class="bg-purple-500/20 text-purple-200 px-2 py-1 rounded-full text-xs">
                                  📁 {source}
                                </span>
                              {/each}
                              {#each step.details.tags_considered as tag}
                                <span class="bg-purple-500/20 text-purple-200 px-2 py-1 rounded-full text-xs">
                                  #{tag}
                                </span>
                              {/each}
                            </div>
                          </div>
                        </div>
                      {:else if step.type === 'rank'}
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                          <div class="bg-white/5 p-3 rounded-lg">
                            <div class="text-orange-300 font-medium">Ranking Factors</div>
                            <div class="text-white mt-1 space-y-1">
                              {#each step.details.ranking_factors as factor}
                                <div class="text-xs">• {factor.replace(/_/g, ' ')}</div>
                              {/each}
                            </div>
                          </div>
                          <div class="bg-white/5 p-3 rounded-lg">
                            <div class="text-orange-300 font-medium">Top Results</div>
                            <div class="text-white mt-1 text-lg font-bold">{step.details.top_memories}</div>
                          </div>
                          <div class="bg-white/5 p-3 rounded-lg">
                            <div class="text-orange-300 font-medium">Confidence</div>
                            <div class="text-white mt-1 text-lg font-bold">{(step.details.confidence_score * 100).toFixed(0)}%</div>
                          </div>
                        </div>
                      {:else if step.type === 'response'}
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                          <div class="bg-white/5 p-3 rounded-lg">
                            <div class="text-green-300 font-medium">Sources Used</div>
                            <div class="text-white mt-1 text-lg font-bold">{step.details.memory_sources_used}</div>
                          </div>
                          <div class="bg-white/5 p-3 rounded-lg">
                            <div class="text-green-300 font-medium">Confidence</div>
                            <div class="text-white mt-1 text-lg font-bold">{(step.details.response_confidence * 100).toFixed(0)}%</div>
                          </div>
                          <div class="bg-white/5 p-3 rounded-lg">
                            <div class="text-green-300 font-medium">Method</div>
                            <div class="text-white mt-1 text-xs">{step.details.synthesis_method.replace(/_/g, ' ')}</div>
                          </div>
                        </div>
                      {/if}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          {/each}
        </div>

        <!-- Summary -->
        <div class="mt-8 bg-gradient-to-r from-emerald-500/20 to-teal-500/20 backdrop-blur-sm rounded-xl border border-emerald-400/30 p-6">
          <h3 class="text-lg font-bold text-emerald-300 mb-4 flex items-center gap-2">
            ✅ Processing Summary
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div class="bg-white/5 p-3 rounded-lg">
              <div class="text-emerald-300 font-medium">Total Processing Time</div>
              <div class="text-white mt-1 text-lg font-bold">847ms</div>
            </div>
            <div class="bg-white/5 p-3 rounded-lg">
              <div class="text-emerald-300 font-medium">Memory Sources Scanned</div>
              <div class="text-white mt-1 text-lg font-bold">156</div>
            </div>
            <div class="bg-white/5 p-3 rounded-lg">
              <div class="text-emerald-300 font-medium">Overall Confidence</div>
              <div class="text-white mt-1 text-lg font-bold">92%</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
{/if}

<style>
  /* Custom animations for trace steps */
  .bg-gradient-to-r {
    background-attachment: fixed;
  }
</style>

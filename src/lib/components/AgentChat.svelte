<script lang="ts">
import { onMount } from 'svelte';
import { invoke } from '@tauri-apps/api/core';

export let agentId: string;
export let agentName: string = '';

let question = '';
let answer: string | null = null;
let usedMemories: any[] = [];
let loading = false;
let error: string | null = null;

async function askAgent() {
  error = null;
  answer = null;
  usedMemories = [];
  if (!question.trim()) {
    error = 'Please enter a question.';
    return;
  }
  loading = true;
  try {
    const response = await invoke('execute_agent_query_tauri', {
      request: {
        agent_id: agentId,
        question,
        max_results: 5,
        min_similarity: null
      }
    });
    // Response shape: { response, memories_used, ... }
    answer = response.response;
    usedMemories = response.memories_used || [];
    if (!answer && usedMemories.length === 0) {
      error = 'No relevant memories found or agent has no access.';
    }
  } catch (e: any) {
    error = e?.message || 'Failed to get answer from agent.';
  } finally {
    loading = false;
  }
}
</script>

<div class="agent-chat">
  <h2>Ask {agentName || 'Agent'}</h2>
  <form on:submit|preventDefault={askAgent} class="chat-form">
    <input
      type="text"
      placeholder="Ask a question..."
      bind:value={question}
      class="chat-input"
      autocomplete="off"
      disabled={loading}
    />
    <button type="submit" class="ask-btn" disabled={loading || !question.trim()}>
      {#if loading}
        <span class="spinner"></span>
      {:else}
        Ask
      {/if}
    </button>
  </form>

  {#if error}
    <div class="error">{error}</div>
  {/if}

  {#if answer}
    <div class="answer-block">
      <div class="answer-label">AI Answer:</div>
      <div class="answer">{answer}</div>
    </div>
  {/if}

  {#if usedMemories.length > 0}
    <div class="memories-block">
      <div class="memories-label">Memories used for this answer:</div>
      <ul class="memories-list">
        {#each usedMemories as mem, i}
          <li class="memory-item">
            <div class="mem-content">{mem.content}</div>
            <div class="mem-meta">
              <span>Source: <b>{mem.source}</b></span>
              <span>Author: <b>{mem.author}</b></span>
              <span>Date: <b>{new Date(mem.timestamp).toLocaleString()}</b></span>
              {#if mem.tags?.length}
                <span>Tags: {mem.tags.join(', ')}</span>
              {/if}
              <span class="provenance">(Provenance: {mem.provenance?.permissions_used || 'unknown'})</span>
            </div>
          </li>
        {/each}
      </ul>
      <div class="privacy-note">
        <span>🔒 This answer was generated locally. No data left your device.</span>
      </div>
    </div>
  {/if}
</div>

<style>
.agent-chat {
  max-width: 600px;
  margin: 2rem auto;
  background: var(--color-bg, #fff);
  border-radius: 1rem;
  box-shadow: 0 2px 16px rgba(0,0,0,0.07);
  padding: 2rem 2rem 1.5rem 2rem;
}
.agent-chat h2 {
  margin-bottom: 1.2rem;
  font-size: 1.4rem;
  font-weight: 600;
}
.chat-form {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1.2rem;
}
.chat-input {
  flex: 1;
  padding: 0.7rem 1rem;
  border-radius: 0.7rem;
  border: 1px solid #d0d0d0;
  font-size: 1rem;
  background: #fafbfc;
}
.ask-btn {
  background: var(--color-primary, #3b82f6);
  color: #fff;
  border: none;
  border-radius: 0.7rem;
  padding: 0 1.2rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s;
  display: flex;
  align-items: center;
  min-width: 70px;
  justify-content: center;
}
.ask-btn:disabled {
  background: #b3c6e0;
  cursor: not-allowed;
}
.spinner {
  width: 1.2em;
  height: 1.2em;
  border: 2px solid #fff;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 0.7s linear infinite;
  display: inline-block;
}
@keyframes spin {
  to { transform: rotate(360deg); }
}
.error {
  color: #d32f2f;
  background: #fff0f0;
  border-radius: 0.5rem;
  padding: 0.7rem 1rem;
  margin-bottom: 1rem;
  font-size: 1rem;
}
.answer-block {
  margin-bottom: 1.5rem;
}
.answer-label {
  font-weight: 600;
  margin-bottom: 0.3rem;
  color: #3b82f6;
}
.answer {
  background: #f4f8ff;
  border-radius: 0.7rem;
  padding: 1rem;
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
}
.memories-block {
  margin-top: 1.5rem;
}
.memories-label {
  font-weight: 600;
  margin-bottom: 0.5rem;
}
.memories-list {
  list-style: none;
  padding: 0;
  margin: 0 0 0.5rem 0;
}
.memory-item {
  background: #f9fafb;
  border-radius: 0.5rem;
  padding: 0.7rem 1rem;
  margin-bottom: 0.7rem;
  font-size: 0.98rem;
  border-left: 3px solid #3b82f6;
}
.mem-content {
  margin-bottom: 0.3rem;
}
.mem-meta {
  font-size: 0.92em;
  color: #666;
  display: flex;
  flex-wrap: wrap;
  gap: 1.2em;
}
.provenance {
  color: #888;
  font-size: 0.9em;
}
.privacy-note {
  margin-top: 0.7rem;
  font-size: 0.97em;
  color: #1e7e34;
  background: #eafbe7;
  border-radius: 0.5rem;
  padding: 0.5rem 1rem;
  display: flex;
  align-items: center;
  gap: 0.5em;
}
</style> 
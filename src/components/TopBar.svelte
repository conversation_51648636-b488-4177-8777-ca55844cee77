<script lang="ts">
  export let title: string = "";
</script>

<header class="w-full h-16 bg-slate-800 border-b border-white/10 flex items-center px-6 justify-between shadow-md">
  <h1 class="text-lg font-semibold text-white hidden md:block">{title}</h1>
  <div class="flex-1 flex justify-center md:justify-end">
    <input
      placeholder="Search memories..."
      class="w-full md:w-64 px-4 py-2 rounded-lg bg-slate-700 placeholder-purple-300 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
    />
  </div>
</header>
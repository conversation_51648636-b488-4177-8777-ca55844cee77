<!doctype html>
<html lang="en" class="dark">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%sveltekit.assets%/favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#7c3aed" />
    <meta name="description" content="MindMesh - Privacy-first memory layer for humans and AI agents" />
    <title>🧠 MindMesh - Privacy-First Memory Layer</title>
    %sveltekit.head%
    <style>
      /* Force cache refresh */
      body { --cache-bust: v2024-12-20-2; }
      /* Prevent flash of unstyled content */
      body { background: linear-gradient(135deg, #0f172a 0%, #581c87 50%, #0f172a 100%); }
      
      /* Blob animations */
      @keyframes blob {
        0% { transform: translate(0px, 0px) scale(1); }
        33% { transform: translate(30px, -50px) scale(1.1); }
        66% { transform: translate(-20px, 20px) scale(0.9); }
        100% { transform: translate(0px, 0px) scale(1); }
      }
      .animate-blob { animation: blob 7s infinite; }
      .animation-delay-2000 { animation-delay: 2s; }
      .animation-delay-4000 { animation-delay: 4s; }
    </style>
  </head>
  <body data-sveltekit-preload-data="hover" class="bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 min-h-screen">
    <div style="display: contents">%sveltekit.body%</div>
  </body>
</html>

# 🧠 MindMesh MVP

A privacy-first memory layer for humans and AI agents.

## ✅ Completed Features

### Phase 1: Core Memory System
1. **Memory Input**: Clean textarea interface for entering memories
2. **Memory Storage**: Local JSONL storage in app data directory  
3. **Memory Display**: Beautiful list view showing all saved memories with metadata
4. **Memory Deletion**: Delete individual memories with confirmation
5. **Real-time Updates**: UI refreshes automatically after save/delete operations

### Phase 2: Local AI Agent
6. **Memory Embeddings**: Uses local Ollama with `nomic-embed-text` for semantic search
7. **Intelligent Queries**: Ask questions about your memories using natural language
8. **Cosine Similarity**: Finds the most relevant memories based on semantic similarity
9. **Local LLM**: Uses `phi3:mini` for generating human-like responses
10. **Privacy-First**: Everything runs locally via Ollama

### Phase 3: Structured Vault System ⭐ **NEW!**
11. **Structured Storage**: Replaced JSONL with organized vault folder structure
12. **Enhanced Metadata**: Tags, permissions, source tracking, and custom metadata
13. **Automatic Migration**: Seamlessly migrates from legacy JSONL format
14. **Encryption Ready**: Built-in encryption support for sensitive data
15. **Agent Permissions**: Framework for multi-agent access control
16. **Source Integration**: Ready for Slack, Email, Git, and other integrations
17. **Smart Indexing**: Fast tag-based search and entry lookup
18. **Vault Statistics**: Real-time stats about your memory vault

## 🏗️ **Vault Structure**

Your memories are now organized in a structured vault:

```
vault/
├── config/
│   ├── vault.json          # Vault metadata & settings
│   ├── agents.json         # Agent definitions & permissions  
│   └── sources.json        # Source configurations
├── entries/
│   ├── 2025/01/            # Year/Month organization
│   │   ├── entry_abc123.json
│   │   └── entry_def456.json
│   └── index.json          # Fast lookup index
├── encryption/
│   ├── device.key          # Device-specific encryption key
│   └── keychain.json       # Key management
└── metadata/
    ├── tags.json           # Tag definitions
    └── relations.json      # Entry relationships
```

## 💾 **Enhanced Memory Format**

Each memory is now a rich, structured entry:

```json
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "content": "User entered text",
  "timestamp": "2025-01-27T23:01:00Z",
  "author": "bipin@mindmesh",
  "source": {
    "source_type": "manual",
    "integration_id": null,
    "metadata": {}
  },
  "tags": ["important", "follow-up"],
  "permissions": {
    "read": ["user:bipin@mindmesh"],
    "write": ["user:bipin@mindmesh"]
  },
  "encryption": {
    "encrypted": false,
    "key_id": null
  },
  "metadata": {
    "location": null,
    "priority": null,
    "related_entries": [],
    "embedding_vector": [0.1, 0.2, ...], 
    "custom": {}
  }
}
```

## 🔄 **Automatic Migration**

- ✅ **Seamless**: Existing JSONL memories auto-migrate to vault format
- ✅ **Preserves Data**: All content, timestamps, and metadata preserved
- ✅ **Backup**: Original JSONL file renamed to `.backup` 
- ✅ **No Downtime**: Migration happens transparently on first run

## 🚀 **Getting Started**

### Prerequisites

**1. Install Ollama**
```bash
# macOS
brew install ollama

# Or visit: https://ollama.ai/download
```

**2. Install AI Models**
```bash
# Embedding model (for semantic search)
ollama pull nomic-embed-text

# Chat model (for responses)  
ollama pull phi3:mini

# Verify
ollama list
```

**3. Start Ollama**
```bash
ollama serve
```

### Running MindMesh

**Development:**
```bash
npm install
npm run tauri dev
```

**Production:**
```bash
npm run tauri build
```

## 🎯 **How It Works**

1. **Save Memories**: Type anything into the input field - thoughts, notes, reminders
2. **Automatic Processing**: Each memory gets embedded using local AI for semantic search
3. **Ask Questions**: Use natural language to query your memories
4. **Intelligent Responses**: AI finds relevant memories and provides contextual answers
5. **Structured Storage**: Everything organized in a searchable, encrypted vault

## 🔮 **What's Next**

The vault system is now ready for:
- **Multi-source Integration**: Slack, Email, Git commits, etc.
- **Agent Collaboration**: Multiple AI agents with different permissions
- **Advanced Search**: Tag filtering, date ranges, similarity thresholds
- **Data Encryption**: User-controlled encryption for sensitive memories
- **Export/Import**: Backup and sync capabilities

## 🛡️ **Privacy**

- ✅ **100% Local**: All data stays on your device
- ✅ **No Cloud**: No external APIs or servers
- ✅ **Encrypted Storage**: Built-in encryption support
- ✅ **Open Source**: Full transparency and control

---

**Example Usage:**

*User saves:* "Need to follow up with John about the project proposal by Friday"

*Later asks:* "What do I need to follow up on?"

*AI responds:* "You need to follow up with John about the project proposal by Friday. This was saved earlier today."

## 🔧 **Technical Stack**

- **Frontend**: Svelte + TypeScript + Tailwind CSS
- **Backend**: Rust + Tauri
- **AI**: Local Ollama (nomic-embed-text + phi3:mini)
- **Storage**: Structured JSON vault with indexing
- **Search**: Semantic similarity via cosine distance

## 🛠️ Setup Requirements

### 1. Install Ollama
```bash
# macOS
brew install ollama

# Linux/Windows - visit https://ollama.ai/download
```

### 2. Install Required Models
```bash
# Pull the embedding model
ollama pull nomic-embed-text

# Pull the chat model (small and fast)
ollama pull phi3:mini

# Start Ollama server
ollama serve
```

### 3. Run MindMesh
```bash
# Install dependencies
npm install

# Start the app
npm run tauri dev
```

## 🚀 Usage Examples

### Saving Memories
- "Remember to follow up with John about the project proposal"
- "Meeting with Sarah tomorrow at 2pm about budget planning"
- "Found a great restaurant: 'Blue Table' on 5th street"

### Querying Memories
- "What did I forget to follow up on?"
- "When is my meeting with Sarah?"
- "What restaurants did I save?"
- "What meetings do I have this week?"

## 🔒 Privacy Features

- **Local-Only**: All data stays on your device
- **No Cloud**: No external API calls or data transmission
- **Encrypted Storage**: Files stored in secure app data directory
- **Open Source**: Full transparency of code and data handling

## 📁 Project Structure

```
mindmesh-mvp/
├── src/                    # Svelte frontend
│   └── routes/+page.svelte # Main UI
├── src-tauri/             # Rust backend
│   ├── src/main.rs        # Tauri commands and AI logic
│   └── Cargo.toml         # Rust dependencies
└── memory_log.jsonl       # Local memory storage (auto-created)
```

## 🎯 Next Steps

- [ ] **Memory Categories**: Tag and organize memories
- [ ] **Export/Import**: Backup and restore memory data
- [ ] **Advanced Search**: Time-based and content filtering
- [ ] **Memory Insights**: Analytics and patterns
- [ ] **Multi-Agent Support**: Different AI personas

## 🐛 Troubleshooting

**Ollama Connection Issues:**
- Ensure `ollama serve` is running
- Check that models are installed: `ollama list`
- Verify Ollama is accessible at `http://localhost:11434`

**Performance Issues:**
- Try smaller models: `ollama pull phi3:mini`
- Reduce memory batch size for large collections
- Consider using `llama3.2:1b` for faster inference

---

Built with ❤️ using Tauri, Svelte, and Ollama

# 🤖 MindMesh Agent API

**Phase 3: Permissioned Memory Access for AI Agents**

MindMesh now supports **AI agents with controlled memory access**. Each agent operates with specific permissions, ensuring secure and scoped memory retrieval.

## 🎯 **Core Concepts**

### **Agent Permissions**
- **can_read_sources**: Which data sources the agent can access (e.g., `["slack", "git", "manual"]`)
- **can_read_tags**: Which tagged memories are accessible (e.g., `["urgent", "client:enterprise"]`)  
- **can_write_tags**: What tags the agent can create (future feature)
- **can_read_authors**: Author patterns the agent can see (e.g., `["slack:*", "git:jane.smith"]`)
- **max_memory_age_days**: Temporal scope limit (e.g., only last 30 days)
- **enabled**: Agent on/off switch

### **Memory Provenance**
Every agent response includes **source attribution**:
- Original memory source and timestamp
- Similarity score for semantic matching
- Permission level that granted access
- Source metadata (channel, repo, thread, etc.)

## 🚀 **API Endpoints**

### **1. Agent Query** - Core semantic search with permissions
```javascript
const request = {
  agent_id: "onboarding_bot",
  question: "What were our client issues last week?",
  max_results: 5,        // Optional: limit results
  min_similarity: 0.1    // Optional: similarity threshold
};

const response = await invoke("agent_query", { request });
// Returns: AgentQueryResponse with filtered memories + LLM response
```

### **2. Agent Management**
```javascript
// Get all agents
const agents = await invoke("get_all_agents");

// Get specific agent permissions  
const permissions = await invoke("get_agent_permissions", { 
  agent_id: "revops_agent" 
});

// Create/update agent permissions
await invoke("set_agent_permissions", { permissions: agentConfig });

// Delete agent
await invoke("delete_agent", { agent_id: "old_bot" });

// Get memories accessible to specific agent
const memories = await invoke("get_agent_permitted_memories", { 
  agent_id: "support_agent" 
});

// Create default demo agents
await invoke("create_default_agents");
```

## 🛡️ **Built-in Agent Profiles**

### **1. Onboarding Bot** (`onboarding_bot`)
```json
{
  "agent_id": "onboarding_bot",
  "name": "Onboarding Bot", 
  "can_read_sources": ["manual", "slack"],
  "can_read_tags": ["onboarding", "help", "getting-started"],
  "can_read_authors": ["slack:*", "bipin@mindmesh"],
  "max_memory_age_days": 30,
  "enabled": true
}
```
**Use Case**: Help new users get started, access recent help docs and onboarding discussions.

### **2. RevOps Agent** (`revops_agent`)  
```json
{
  "agent_id": "revops_agent",
  "name": "Revenue Operations Agent",
  "can_read_sources": ["slack", "email", "git"],
  "can_read_tags": ["sales", "revenue", "client", "enterprise"], 
  "can_read_authors": ["slack:*", "git:*"],
  "max_memory_age_days": 90,
  "enabled": true
}
```
**Use Case**: Analyze revenue trends, client feedback, and development progress.

### **3. Support Agent** (`support_agent`)
```json
{
  "agent_id": "support_agent", 
  "name": "Customer Support Agent",
  "can_read_sources": ["slack", "email", "manual"],
  "can_read_tags": ["support", "bug", "feedback", "customer"],
  "can_read_authors": ["slack:*", "support@*"],
  "max_memory_age_days": 60,
  "enabled": true
}
```
**Use Case**: Handle customer issues, access support tickets and feedback.

## 🎮 **Usage Examples**

### **Basic Agent Query**
```javascript
// Query as onboarding bot
const response = await invoke("agent_query", {
  request: {
    agent_id: "onboarding_bot",
    question: "How do new users set up their account?"
  }
});

console.log(response.response); // LLM answer based on permitted memories
console.log(response.memories_used); // Source memories with provenance
```

### **Permission Filtering in Action**

1. **Memory exists**: `"Fix the critical auth bug"` tagged `["urgent", "security"]`
2. **onboarding_bot query**: Can't see it (no "security" tag permission)
3. **support_agent query**: Can see it (has broader tag access)
4. **Result**: Same question, different accessible memories per agent

### **Creating Custom Agent**
```javascript
const customAgent = {
  agent_id: "data_analyst", 
  name: "Data Analysis Agent",
  description: "Analyzes user behavior and metrics",
  can_read_sources: ["manual", "git"],
  can_read_tags: ["analytics", "metrics", "data"],
  can_write_tags: ["analysis", "insight"], 
  can_read_authors: ["bipin@mindmesh", "git:*"],
  max_memory_age_days: 180,
  enabled: true,
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString()
};

await invoke("set_agent_permissions", { permissions: customAgent });
```

## 🔐 **Security Model**

### **Permission Inheritance**
- **Default**: Agents can only read memories they have explicit permission for
- **Deny by Default**: No permissions = no memory access
- **Granular Control**: Source + Tag + Author + Time filtering
- **Wildcard Support**: `"slack:*"` matches all Slack users

### **Memory Isolation**  
- Agent A with `can_read_tags: ["sales"]` cannot see memories tagged `["engineering"]`
- Agent B with `max_memory_age_days: 30` cannot see older memories
- Agent permissions are checked at **query time**, not storage time

### **Audit Trail**
Every agent response includes:
```json
{
  "provenance": {
    "source_type": "slack",
    "permissions_used": "agent:onboarding_bot",
    "source_metadata": {"channel": "general"}
  }
}
```

## 🌟 **Advanced Features**

### **Multi-Agent Collaboration**
```javascript
// Agent 1: Collect technical details
const techResponse = await invoke("agent_query", {
  request: { 
    agent_id: "support_agent",
    question: "What are the recent technical issues?"
  }
});

// Agent 2: Analyze business impact  
const bizResponse = await invoke("agent_query", {
  request: {
    agent_id: "revops_agent", 
    question: "How do these issues affect revenue?"
  }
});

// Combine insights from both agents
```

### **Dynamic Permission Updates**
```javascript
// Temporarily expand agent permissions for specific task
const agent = await invoke("get_agent_permissions", { agent_id: "support_agent" });
agent.can_read_tags.push("internal-tools");
agent.updated_at = new Date().toISOString();

await invoke("set_agent_permissions", { permissions: agent });

// ... perform task ...

// Restore original permissions
agent.can_read_tags = agent.can_read_tags.filter(tag => tag !== "internal-tools");
await invoke("set_agent_permissions", { permissions: agent });
```

### **Permission Testing**
```javascript
// Test what memories an agent can access
const accessibleMemories = await invoke("get_agent_permitted_memories", {
  agent_id: "onboarding_bot"
});

console.log(`Agent can access ${accessibleMemories.length} memories`);
```

## 🔮 **What's Next**

- **HTTP API**: External agent access via REST endpoints
- **WebSocket Streaming**: Real-time agent responses
- **Agent-to-Agent Communication**: Controlled memory sharing between agents  
- **Fine-grained Write Permissions**: Agent memory creation with approval workflows
- **ZK-Proof Sharing**: Cryptographic memory sharing between different MindMesh instances

---

## 🧪 **Quick Start Testing**

1. **Create Default Agents**: Click "Create Default Agents" in the UI
2. **Add Test Memories**: Save some memories with different tags (`onboarding`, `support`, `sales`)
3. **Test Agent Queries**: 
   - Select "Onboarding Bot" 
   - Ask: *"What help resources are available?"*
   - Compare with "Support Agent" asking the same question
4. **Check Provenance**: Expand "View Source Memories" to see permission filtering in action

**The Agent API is ready for production! 🚀**

All processing happens **locally** with **privacy-first** design. Perfect for multi-agent systems that need controlled memory access. 
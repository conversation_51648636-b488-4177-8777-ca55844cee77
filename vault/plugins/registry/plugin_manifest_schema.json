{"schema_version": "1.0", "template": {"id": "plugin-unique-id", "name": "Plugin Display Name", "version": "1.0.0", "description": "Brief description of what the plugin does", "author": "author-username", "license": "MIT", "mindmesh_version": ">=1.0.0", "plugin_type": "MemorySource|AgentBehavior|UIComponent|DataProcessor|Integration", "capabilities": {"can_read_memories": false, "can_write_memories": false, "can_access_network": false, "can_execute_code": false, "can_modify_agents": false, "required_apis": []}, "permissions": {"memory_sources": [], "agent_access": [], "file_system_access": false, "network_domains": [], "sensitive_data_access": false}, "entry_point": "main.wasm", "signatures": {"author_signature": "", "code_hash": "", "mindmesh_verified": false, "community_rating": 0.0}}, "required_fields": ["id", "name", "version", "description", "author", "plugin_type", "capabilities", "permissions", "entry_point"]}
# 🔌 MindMesh Ingestion API

**Phase 2: Multi-Agent Memory Ingestion**

MindMesh now supports external memory ingestion from tools like Slack, Git, Gmail, and custom sources through a plugin-friendly API.

## 🚀 **API Endpoints**

### **1. Slack Memory Ingestion**
```javascript
// Endpoint: ingest_slack_memory
await invoke("ingest_slack_memory", { 
  request: {
    text: "Just finished the project review - looking good!",
    channel: "general", 
    author: "john.doe",
    timestamp: "2025-01-27T15:30:00Z", // Optional, ISO 8601
    thread_ts: "1643284200.123456",   // Optional
    message_type: "message"           // Optional
  }
});
```

**Auto-Generated Tags**: `["slack", "channel:general"]`
**Author Format**: `slack:john.doe`
**Source Metadata**: Channel, thread info, message type

### **2. Git Commit Ingestion**
```javascript
// Endpoint: ingest_git_memory
await invoke("ingest_git_memory", {
  request: {
    commit_message: "Add user authentication feature",
    author: "jane.smith",
    repo: "mindmesh-mvp",
    branch: "main", 
    commit_hash: "a1b2c3d4e5f6",
    timestamp: "2025-01-27T16:45:00Z" // Optional
  }
});
```

**Auto-Generated Tags**: `["git", "repo:mindmesh-mvp", "branch:main"]`
**Author Format**: `git:jane.smith`
**Source Metadata**: Repo, branch, commit hash

### **3. Generic Memory Ingestion**
```javascript
// Endpoint: ingest_generic_memory
await invoke("ingest_generic_memory", {
  request: {
    content: "Customer feedback: Love the new dashboard!",
    source_type: "email",
    author: "<EMAIL>",
    integration_id: "gmail-inbox",     // Optional
    metadata: {                       // Optional
      "subject": "Product Feedback",
      "thread_id": "xyz123"
    },
    tags: ["feedback", "positive"],   // Optional
    timestamp: "2025-01-27T14:20:00Z" // Optional
  }
});
```

**Custom Source Support**: Any source type
**Flexible Metadata**: Key-value pairs for source-specific data
**Custom Tags**: User-defined categorization

## 🔍 **Search & Filtering API**

### **Advanced Memory Search**
```javascript
// Endpoint: search_memories
await invoke("search_memories", {
  request: {
    source_filter: "slack",           // Optional: Filter by source
    tag_filter: ["urgent", "follow-up"], // Optional: Filter by tags
    author_filter: "john",           // Optional: Search in author names
    start_date: "2025-01-01T00:00:00Z", // Optional: Date range start
    end_date: "2025-01-31T23:59:59Z"    // Optional: Date range end
  }
});
```

### **Get Available Filters**
```javascript
// Get all sources
const sources = await invoke("get_sources");
// Returns: ["manual", "slack", "git", "email"]

// Get all tags
const tags = await invoke("get_tags");
// Returns: ["slack", "channel:general", "urgent", "git", ...]
```

## 🔗 **Zapier Integration**

### **Setup Workflow**

1. **Create Zapier Webhook**
   - Trigger: Slack new message, Gmail new email, etc.
   - Action: POST to MindMesh API endpoint

2. **Configure Webhook URL**
   ```
   POST http://localhost:11434/api/ingest
   ```

3. **Zapier Action Configuration**
   ```json
   {
     "source_type": "slack",
     "text": "{{message.text}}",
     "channel": "{{channel.name}}",
     "author": "{{user.name}}",
     "timestamp": "{{message.timestamp}}"
   }
   ```

### **Example Zapier Workflows**

**Slack → MindMesh**
```
Trigger: New message in #general
Filter: Contains keywords ["important", "deadline", "meeting"]
Action: Call MindMesh ingest_slack_memory
```

**Gmail → MindMesh**
```
Trigger: New email with label "Important"
Action: Call MindMesh ingest_generic_memory with source_type="email"
```

**GitHub → MindMesh**
```
Trigger: New commit to main branch
Action: Call MindMesh ingest_git_memory
```

## 📊 **Memory Format**

Each ingested memory follows this structured format:

```json
{
  "id": "uuid-here",
  "content": "The actual message/commit/content",
  "timestamp": "2025-01-27T15:30:00Z",
  "author": "source:username",
  "source": {
    "source_type": "slack",
    "integration_id": "slack-general",
    "metadata": {
      "channel": "general",
      "thread_ts": "123456789.123"
    }
  },
  "tags": ["slack", "channel:general"],
  "permissions": {
    "read": ["user:author", "agent:*"],
    "write": ["user:author"]
  },
  "metadata": {
    "embedding_vector": [0.1, 0.2, ...],
    "custom": {}
  }
}
```

## 🔒 **Permissions & Privacy**

### **Default Permissions**
- **Read Access**: Original author + all agents
- **Write Access**: Original author only
- **Local Processing**: All embeddings generated locally
- **No External APIs**: Data never leaves your machine

### **Agent Collaboration Ready**
- Multiple agents can read external memories
- Permission scoping by source type
- Future: ZK-proof sharing between agents

## 🎯 **Usage Examples**

### **1. Save Slack Important Messages**
```bash
# Simulate Zapier webhook
curl -X POST localhost:11434/api/ingest \
  -H "Content-Type: application/json" \
  -d '{
    "source_type": "slack",
    "text": "Q4 planning meeting moved to Friday 2pm",
    "channel": "team-updates",
    "author": "manager"
  }'
```

### **2. Track Git Progress**
```javascript
// Auto-ingest commits during CI/CD
const gitHook = async (commitData) => {
  await ingestGitMemory({
    commit_message: commitData.message,
    author: commitData.author,
    repo: "my-project",
    branch: commitData.branch,
    commit_hash: commitData.hash
  });
};
```

### **3. Capture Email Insights**
```javascript
// Gmail addon integration
const emailToMemory = async (email) => {
  await ingestGenericMemory({
    content: `${email.subject}: ${email.body}`,
    source_type: "email",
    author: email.from,
    metadata: {
      subject: email.subject,
      thread_id: email.threadId
    },
    tags: ["email", email.label]
  });
};
```

## 🔮 **What's Next**

- **OAuth Integration**: Direct Slack/Gmail/GitHub connections
- **Real-time Sync**: WebSocket-based live ingestion
- **Agent Permissions**: Fine-grained access control
- **Cross-Agent Sharing**: Encrypted memory sharing between agents
- **Smart Categorization**: AI-powered auto-tagging

---

**Ready for Testing!** 🚀

The ingestion system is fully functional and ready for:
1. **Zapier workflows** 
2. **Direct API integration**
3. **Custom plugin development**
4. **Multi-agent collaboration** 
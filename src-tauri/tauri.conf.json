{"$schema": "https://schema.tauri.app/config/2", "productName": "mindmesh-mvp", "version": "0.1.0", "identifier": "com.mindmesh.app", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "npm run build", "frontendDist": "../build"}, "app": {"windows": [{"title": "MindMesh - AI Agent System", "width": 1200, "height": 800, "resizable": true, "fullscreen": false, "visible": true, "focus": true, "alwaysOnTop": false, "url": "index.html"}], "security": {"csp": null, "dangerousDisableAssetCspModification": true}, "withGlobalTauri": true}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "macOS": {"entitlements": "entitlements.plist", "exceptionDomain": "", "frameworks": [], "providerShortName": null, "signingIdentity": null}}, "plugins": {"fs": {"scope": ["$HOME/Documents/**", "$HOME/Library/Application Support/**", "$HOME/Downloads/**"]}, "dialog": {"open": true, "save": true}, "opener": {"enable": true}}}
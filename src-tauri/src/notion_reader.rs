use std::path::PathBuf;
use std::fs;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct NotionPage {
    pub id: String,
    pub title: String,
    pub content: String,
    pub created_time: DateTime<Utc>,
    pub last_edited_time: DateTime<Utc>,
    pub properties: NotionProperties,
    pub file_path: PathBuf,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct NotionProperties {
    pub tags: Vec<String>,
    pub status: Option<String>,
    pub priority: Option<String>,
    pub assignee: Option<String>,
    pub due_date: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NotionDatabase {
    pub id: String,
    pub name: String,
    pub pages: Vec<NotionPage>,
    pub properties: Vec<String>,
}

pub struct NotionReader {
    pub granted_paths: Vec<PathBuf>,
}

impl NotionReader {
    pub fn new(granted_paths: Vec<PathBuf>) -> Self {
        Self { granted_paths }
    }

    /// Scan for Notion files in granted directories
    pub fn scan_notion_files(&self) -> Result<Vec<NotionPage>, String> {
        let mut pages = Vec::new();

        for path in &self.granted_paths {
            if !path.exists() {
                continue;
            }

            // Scan for different types of Notion exports
            pages.extend(self.scan_directory_for_notion_files(path)?);
        }

        Ok(pages)
    }

    /// Scan a directory for Notion files
    fn scan_directory_for_notion_files(&self, dir: &PathBuf) -> Result<Vec<NotionPage>, String> {
        let mut pages = Vec::new();

        if !dir.is_dir() {
            return Ok(pages);
        }

        let entries = fs::read_dir(dir).map_err(|e| e.to_string())?;

        for entry in entries {
            let entry = entry.map_err(|e| e.to_string())?;
            let path = entry.path();

            if path.is_dir() {
                // Recursively scan subdirectories
                pages.extend(self.scan_directory_for_notion_files(&path)?);
            } else if self.is_notion_file(&path) {
                if let Ok(page) = self.parse_notion_file(&path) {
                    pages.push(page);
                }
            }
        }

        Ok(pages)
    }

    /// Check if a file is a Notion export file
    fn is_notion_file(&self, path: &PathBuf) -> bool {
        if let Some(extension) = path.extension() {
            let ext = extension.to_string_lossy().to_lowercase();
            match ext.as_str() {
                "md" | "html" | "csv" | "json" => true,
                _ => false,
            }
        } else {
            false
        }
    }

    /// Parse a Notion file and extract content
    fn parse_notion_file(&self, path: &PathBuf) -> Result<NotionPage, String> {
        let content = fs::read_to_string(path).map_err(|e| e.to_string())?;
        let metadata = fs::metadata(path).map_err(|e| e.to_string())?;

        let title = self.extract_title_from_filename(path);
        let (parsed_content, properties) = self.parse_content_and_properties(&content, path)?;

        let created_time = metadata.created()
            .map(|t| DateTime::<Utc>::from(t))
            .unwrap_or_else(|_| Utc::now());

        let last_edited_time = metadata.modified()
            .map(|t| DateTime::<Utc>::from(t))
            .unwrap_or_else(|_| Utc::now());

        Ok(NotionPage {
            id: format!("notion_{}", path.file_name().unwrap_or_default().to_string_lossy()),
            title,
            content: parsed_content,
            created_time,
            last_edited_time,
            properties,
            file_path: path.clone(),
        })
    }

    /// Extract title from filename
    fn extract_title_from_filename(&self, path: &PathBuf) -> String {
        path.file_stem()
            .unwrap_or_default()
            .to_string_lossy()
            .to_string()
    }

    /// Parse content and extract properties based on file type
    fn parse_content_and_properties(&self, content: &str, path: &PathBuf) -> Result<(String, NotionProperties), String> {
        let extension = path.extension()
            .unwrap_or_default()
            .to_string_lossy()
            .to_lowercase();

        match extension.as_str() {
            "md" => self.parse_markdown_content(content),
            "html" => self.parse_html_content(content),
            "json" => self.parse_json_content(content),
            "csv" => self.parse_csv_content(content),
            _ => Ok((content.to_string(), NotionProperties::default())),
        }
    }

    /// Parse Markdown content (common Notion export format)
    fn parse_markdown_content(&self, content: &str) -> Result<(String, NotionProperties), String> {
        let mut properties = NotionProperties::default();
        let mut clean_content = String::new();
        let mut in_frontmatter = false;

        for line in content.lines() {
            // Check for YAML frontmatter
            if line.trim() == "---" {
                in_frontmatter = !in_frontmatter;
                continue;
            }

            if in_frontmatter {
                // Parse YAML properties
                if let Some((key, value)) = line.split_once(':') {
                    let key = key.trim().to_lowercase();
                    let value = value.trim().trim_matches('"');

                    match key.as_str() {
                        "tags" => {
                            properties.tags = value.split(',')
                                .map(|s| s.trim().to_string())
                                .collect();
                        }
                        "status" => properties.status = Some(value.to_string()),
                        "priority" => properties.priority = Some(value.to_string()),
                        "assignee" => properties.assignee = Some(value.to_string()),
                        _ => {}
                    }
                }
            } else {
                // Extract tags from content (e.g., #tag)
                if line.contains('#') {
                    let tags: Vec<String> = line.split_whitespace()
                        .filter(|word| word.starts_with('#'))
                        .map(|tag| tag[1..].to_string())
                        .collect();
                    properties.tags.extend(tags);
                }

                clean_content.push_str(line);
                clean_content.push('\n');
            }
        }

        Ok((clean_content.trim().to_string(), properties))
    }

    /// Parse HTML content (Notion web export)
    fn parse_html_content(&self, content: &str) -> Result<(String, NotionProperties), String> {
        // Simple HTML tag removal for now
        let clean_content = content
            .replace("<br>", "\n")
            .replace("<p>", "\n")
            .replace("</p>", "")
            .replace("<div>", "")
            .replace("</div>", "")
            .replace("<span>", "")
            .replace("</span>", "");

        // Remove HTML tags using regex-like approach
        let mut result = String::new();
        let mut in_tag = false;

        for ch in clean_content.chars() {
            match ch {
                '<' => in_tag = true,
                '>' => in_tag = false,
                _ if !in_tag => result.push(ch),
                _ => {}
            }
        }

        Ok((result.trim().to_string(), NotionProperties::default()))
    }

    /// Parse JSON content (Notion API export)
    fn parse_json_content(&self, content: &str) -> Result<(String, NotionProperties), String> {
        // Try to parse as Notion API JSON
        if let Ok(json) = serde_json::from_str::<serde_json::Value>(content) {
            let mut properties = NotionProperties::default();
            let mut text_content = String::new();

            // Extract content from Notion API format
            if let Some(blocks) = json.get("blocks").and_then(|b| b.as_array()) {
                for block in blocks {
                    if let Some(text) = self.extract_text_from_notion_block(block) {
                        text_content.push_str(&text);
                        text_content.push('\n');
                    }
                }
            }

            // Extract properties
            if let Some(props) = json.get("properties").and_then(|p| p.as_object()) {
                for (key, value) in props {
                    match key.as_str() {
                        "Tags" | "tags" => {
                            if let Some(tags) = value.get("multi_select").and_then(|ms| ms.as_array()) {
                                properties.tags = tags.iter()
                                    .filter_map(|tag| tag.get("name").and_then(|n| n.as_str()))
                                    .map(|s| s.to_string())
                                    .collect();
                            }
                        }
                        "Status" | "status" => {
                            if let Some(status) = value.get("select").and_then(|s| s.get("name")).and_then(|n| n.as_str()) {
                                properties.status = Some(status.to_string());
                            }
                        }
                        _ => {}
                    }
                }
            }

            Ok((text_content.trim().to_string(), properties))
        } else {
            Ok((content.to_string(), NotionProperties::default()))
        }
    }

    /// Extract text from a Notion block
    fn extract_text_from_notion_block(&self, block: &serde_json::Value) -> Option<String> {
        if let Some(block_type) = block.get("type").and_then(|t| t.as_str()) {
            match block_type {
                "paragraph" | "heading_1" | "heading_2" | "heading_3" => {
                    block.get(block_type)
                        .and_then(|content| content.get("rich_text"))
                        .and_then(|rt| rt.as_array())
                        .map(|texts| {
                            texts.iter()
                                .filter_map(|text| text.get("plain_text").and_then(|pt| pt.as_str()))
                                .collect::<Vec<_>>()
                                .join("")
                        })
                }
                _ => None,
            }
        } else {
            None
        }
    }

    /// Parse CSV content (database export)
    fn parse_csv_content(&self, content: &str) -> Result<(String, NotionProperties), String> {
        // Simple CSV parsing for Notion database exports
        let lines: Vec<&str> = content.lines().collect();
        if lines.is_empty() {
            return Ok((content.to_string(), NotionProperties::default()));
        }

        // First line is usually headers
        let headers: Vec<&str> = lines[0].split(',').collect();
        let mut formatted_content = String::new();

        for line in lines.iter().skip(1) {
            let values: Vec<&str> = line.split(',').collect();
            for (i, value) in values.iter().enumerate() {
                if let Some(header) = headers.get(i) {
                    formatted_content.push_str(&format!("{}: {}\n", header.trim(), value.trim()));
                }
            }
            formatted_content.push('\n');
        }

        Ok((formatted_content, NotionProperties::default()))
    }

    /// Read specific Notion page by ID
    pub fn read_notion_page(&self, page_id: &str) -> Result<Option<NotionPage>, String> {
        let pages = self.scan_notion_files()?;
        Ok(pages.into_iter().find(|page| page.id == page_id))
    }

    /// Search Notion pages by content
    pub fn search_notion_pages(&self, query: &str) -> Result<Vec<NotionPage>, String> {
        let pages = self.scan_notion_files()?;
        let query_lower = query.to_lowercase();

        let matching_pages = pages.into_iter()
            .filter(|page| {
                page.title.to_lowercase().contains(&query_lower) ||
                page.content.to_lowercase().contains(&query_lower) ||
                page.properties.tags.iter().any(|tag| tag.to_lowercase().contains(&query_lower))
            })
            .collect();

        Ok(matching_pages)
    }
}

impl Default for NotionProperties {
    fn default() -> Self {
        Self {
            tags: Vec::new(),
            status: None,
            priority: None,
            assignee: None,
            due_date: None,
        }
    }
}

use std::path::PathBuf;
use std::fs;
use std::collections::HashMap;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use crate::app_integrations::{AppIntegration, AppType, ConnectionStatus};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemPermissionRequest {
    pub id: String,
    pub integration_id: String,
    pub app_name: String,
    pub app_type: AppType,
    pub requested_paths: Vec<PathBuf>,
    pub justification: String,
    pub status: PermissionRequestStatus,
    pub created_at: DateTime<Utc>,
    pub responded_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PermissionRequestStatus {
    Pending,
    Approved,
    Denied,
    Expired,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct FileAccessInfo {
    pub path: PathBuf,
    pub file_type: String,
    pub size: u64,
    pub last_modified: DateTime<Utc>,
    pub accessible: bool,
}

pub struct SystemPermissionManager {
    vault_path: PathBuf,
    integrations: HashMap<String, AppIntegration>,
    permission_requests: HashMap<String, SystemPermissionRequest>,
}

impl SystemPermissionManager {
    pub fn new(vault_path: PathBuf) -> Result<Self, String> {
        let integrations_path = vault_path.join("config").join("system_integrations.json");
        let requests_path = vault_path.join("config").join("system_permission_requests.json");

        let integrations = if integrations_path.exists() {
            let content = fs::read_to_string(&integrations_path).map_err(|e| e.to_string())?;
            serde_json::from_str(&content).unwrap_or_default()
        } else {
            HashMap::new()
        };

        let permission_requests = if requests_path.exists() {
            let content = fs::read_to_string(&requests_path).map_err(|e| e.to_string())?;
            serde_json::from_str(&content).unwrap_or_default()
        } else {
            HashMap::new()
        };

        Ok(SystemPermissionManager {
            vault_path,
            integrations,
            permission_requests,
        })
    }

    pub fn create_integration(&mut self, app_type: AppType, name: String, description: String) -> Result<String, String> {
        let integration = AppIntegration::new(app_type, name, description);
        let integration_id = integration.id.clone();
        
        self.integrations.insert(integration_id.clone(), integration);
        self.save_integrations()?;
        
        Ok(integration_id)
    }

    pub fn request_system_access(&mut self, integration_id: &str) -> Result<String, String> {
        let integration = self.integrations.get_mut(integration_id)
            .ok_or("Integration not found")?;

        let requested_paths = integration.request_system_permissions();
        
        let request = SystemPermissionRequest {
            id: uuid::Uuid::new_v4().to_string(),
            integration_id: integration_id.to_string(),
            app_name: integration.name.clone(),
            app_type: integration.app_type.clone(),
            requested_paths,
            justification: format!("Access required to read {} data from local system", integration.name),
            status: PermissionRequestStatus::Pending,
            created_at: Utc::now(),
            responded_at: None,
        };

        let request_id = request.id.clone();
        self.permission_requests.insert(request_id.clone(), request);
        
        self.save_integrations()?;
        self.save_permission_requests()?;
        
        Ok(request_id)
    }

    pub fn approve_system_access(&mut self, request_id: &str) -> Result<(), String> {
        // Get request data without holding mutable reference
        let (integration_id, requested_paths) = {
            let request = self.permission_requests.get(request_id)
                .ok_or("Permission request not found")?;
            (request.integration_id.clone(), request.requested_paths.clone())
        };

        // Check if paths are actually accessible
        let mut accessible_paths = Vec::new();
        for path in &requested_paths {
            if self.check_path_accessibility(path) {
                accessible_paths.push(path.clone());
            }
        }

        // Update integration
        if let Some(integration) = self.integrations.get_mut(&integration_id) {
            integration.grant_permissions(accessible_paths);
            integration.update_connection_status(ConnectionStatus::Connected);
        }

        // Update request status
        if let Some(request) = self.permission_requests.get_mut(request_id) {
            request.status = PermissionRequestStatus::Approved;
            request.responded_at = Some(Utc::now());
        }

        self.save_integrations()?;
        self.save_permission_requests()?;

        Ok(())
    }

    pub fn deny_system_access(&mut self, request_id: &str) -> Result<(), String> {
        // Get integration ID without holding mutable reference
        let integration_id = {
            let request = self.permission_requests.get(request_id)
                .ok_or("Permission request not found")?;
            request.integration_id.clone()
        };

        // Update integration
        if let Some(integration) = self.integrations.get_mut(&integration_id) {
            integration.deny_permissions();
        }

        // Update request status
        if let Some(request) = self.permission_requests.get_mut(request_id) {
            request.status = PermissionRequestStatus::Denied;
            request.responded_at = Some(Utc::now());
        }

        self.save_integrations()?;
        self.save_permission_requests()?;

        Ok(())
    }

    pub fn get_pending_requests(&self) -> Vec<&SystemPermissionRequest> {
        self.permission_requests.values()
            .filter(|req| matches!(req.status, PermissionRequestStatus::Pending))
            .collect()
    }

    pub fn get_all_integrations(&self) -> Vec<&AppIntegration> {
        self.integrations.values().collect()
    }

    pub fn get_integration(&self, integration_id: &str) -> Option<&AppIntegration> {
        self.integrations.get(integration_id)
    }

    pub fn scan_app_files(&self, integration_id: &str) -> Result<Vec<FileAccessInfo>, String> {
        let integration = self.integrations.get(integration_id)
            .ok_or("Integration not found")?;

        let mut files = Vec::new();
        
        for data_source in &integration.data_sources {
            if !data_source.enabled {
                continue;
            }

            if integration.can_access_path(&data_source.path) {
                files.extend(self.scan_directory(&data_source.path, &data_source.file_patterns)?);
            }
        }

        Ok(files)
    }

    fn scan_directory(&self, path: &PathBuf, patterns: &[String]) -> Result<Vec<FileAccessInfo>, String> {
        let mut files = Vec::new();
        
        if !path.exists() {
            return Ok(files);
        }

        if path.is_file() {
            if self.matches_patterns(&path, patterns) {
                files.push(self.get_file_info(path)?);
            }
            return Ok(files);
        }

        if path.is_dir() {
            let entries = fs::read_dir(path).map_err(|e| e.to_string())?;
            
            for entry in entries {
                let entry = entry.map_err(|e| e.to_string())?;
                let entry_path = entry.path();
                
                if entry_path.is_file() && self.matches_patterns(&entry_path, patterns) {
                    files.push(self.get_file_info(&entry_path)?);
                } else if entry_path.is_dir() && files.len() < 100 { // Limit to prevent infinite recursion
                    files.extend(self.scan_directory(&entry_path, patterns)?);
                }
            }
        }

        Ok(files)
    }

    fn matches_patterns(&self, path: &PathBuf, patterns: &[String]) -> bool {
        if patterns.is_empty() {
            return true;
        }

        let file_name = path.file_name()
            .and_then(|name| name.to_str())
            .unwrap_or("");

        patterns.iter().any(|pattern| {
            if pattern.starts_with("*.") {
                let extension = &pattern[2..];
                file_name.ends_with(extension)
            } else {
                file_name.contains(pattern)
            }
        })
    }

    fn get_file_info(&self, path: &PathBuf) -> Result<FileAccessInfo, String> {
        let metadata = fs::metadata(path).map_err(|e| e.to_string())?;
        
        let file_type = path.extension()
            .and_then(|ext| ext.to_str())
            .unwrap_or("unknown")
            .to_string();

        Ok(FileAccessInfo {
            path: path.clone(),
            file_type,
            size: metadata.len(),
            last_modified: metadata.modified()
                .map_err(|e| e.to_string())?
                .into(),
            accessible: true,
        })
    }

    fn check_path_accessibility(&self, path: &PathBuf) -> bool {
        // Expand tilde in path
        let expanded_path = if path.starts_with("~") {
            if let Some(home_dir) = dirs::home_dir() {
                home_dir.join(path.strip_prefix("~").unwrap_or(path))
            } else {
                path.clone()
            }
        } else {
            path.clone()
        };

        expanded_path.exists() && fs::metadata(&expanded_path).is_ok()
    }

    pub fn read_file_content(&self, integration_id: &str, file_path: &PathBuf) -> Result<String, String> {
        let integration = self.integrations.get(integration_id)
            .ok_or("Integration not found")?;

        if !integration.can_access_path(file_path) {
            return Err("Access denied: path not in granted permissions".to_string());
        }

        fs::read_to_string(file_path).map_err(|e| e.to_string())
    }

    fn save_integrations(&self) -> Result<(), String> {
        let integrations_path = self.vault_path.join("config").join("system_integrations.json");
        fs::create_dir_all(integrations_path.parent().unwrap()).map_err(|e| e.to_string())?;
        
        let content = serde_json::to_string_pretty(&self.integrations).map_err(|e| e.to_string())?;
        fs::write(&integrations_path, content).map_err(|e| e.to_string())
    }

    fn save_permission_requests(&self) -> Result<(), String> {
        let requests_path = self.vault_path.join("config").join("system_permission_requests.json");
        fs::create_dir_all(requests_path.parent().unwrap()).map_err(|e| e.to_string())?;
        
        let content = serde_json::to_string_pretty(&self.permission_requests).map_err(|e| e.to_string())?;
        fs::write(&requests_path, content).map_err(|e| e.to_string())
    }
}

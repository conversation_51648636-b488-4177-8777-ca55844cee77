// Phase 2: Comprehensive Trace Logging
use std::collections::HashMap;
use std::fs::OpenOptions;
use std::io::Write;
use std::path::PathBuf;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AgentQueryTrace {
    pub trace_id: String,
    pub agent_id: String,
    pub query: String,
    pub memory_sources: Vec<String>,
    pub results_count: usize,
    pub processing_time_ms: u64,
    pub timestamp: DateTime<Utc>,
}

pub struct TraceManager {
    vault_path: PathBuf,
    traces: Vec<AgentQueryTrace>,
}

impl TraceManager {
    pub fn new(vault_path: PathBuf) -> Self {
        TraceManager {
            vault_path,
            traces: Vec::new(),
        }
    }

    pub fn log_agent_query(&mut self, trace: AgentQueryTrace) -> Result<(), String> {
        self.traces.push(trace.clone());
        self.write_trace_to_csv(&trace)?;
        Ok(())
    }

    fn write_trace_to_csv(&self, trace: &AgentQueryTrace) -> Result<(), String> {
        let trace_dir = self.vault_path.join("traces");
        std::fs::create_dir_all(&trace_dir).map_err(|e| e.to_string())?;
        let trace_file = trace_dir.join("agent_queries.csv");
        
        let mut file = OpenOptions::new()
            .create(true)
            .append(true)
            .open(&trace_file)
            .map_err(|e| e.to_string())?;
        
        writeln!(file, "{},{},{},{},{},{}", 
            trace.trace_id, trace.agent_id, trace.query.replace(",", ";"), 
            trace.memory_sources.join("|"), trace.results_count, trace.processing_time_ms)
            .map_err(|e| e.to_string())?;
        
        Ok(())
    }
}

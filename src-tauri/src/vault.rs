use std::collections::HashMap;
use std::fs;
use std::path::{Path, PathBuf};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use aes_gcm::{
    aead::{Aead, KeyInit, OsRng},
    Aes256Gcm, Key, Nonce
};
use rand::RngCore;
use base64::{Engine as _, engine::general_purpose};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct VaultEntry {
    pub id: String,
    pub content: String,
    pub timestamp: DateTime<Utc>,
    pub author: String,
    pub source: EntrySource,
    pub tags: Vec<String>,
    pub permissions: EntryPermissions,
    pub encryption: EncryptionInfo,
    pub metadata: EntryMetadata,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct EntrySource {
    pub source_type: String,
    pub integration_id: Option<String>,
    pub metadata: HashMap<String, serde_json::Value>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct EntryPermissions {
    pub read: Vec<String>,
    pub write: Vec<String>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct EncryptionInfo {
    pub encrypted: bool,
    pub key_id: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EntryMetadata {
    pub location: Option<String>,
    pub priority: Option<String>,
    pub related_entries: Vec<String>,
    pub embedding_vector: Option<Vec<f64>>,
    pub custom: HashMap<String, serde_json::Value>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct VaultConfig {
    pub version: String,
    pub created_at: DateTime<Utc>,
    pub encryption_enabled: bool,
    pub default_permissions: EntryPermissions,
    pub settings: HashMap<String, serde_json::Value>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct VaultIndex {
    pub entries: HashMap<String, EntryIndexInfo>,
    pub tags: HashMap<String, Vec<String>>, // tag -> entry_ids
    pub last_updated: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct EntryIndexInfo {
    pub id: String,
    pub timestamp: DateTime<Utc>,
    pub author: String,
    pub source_type: String,
    pub tags: Vec<String>,
    pub file_path: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AgentConfig {
    pub agents: HashMap<String, Agent>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Agent {
    pub id: String,
    pub name: String,
    pub permissions: Vec<String>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct AgentPermissions {
    pub agent_id: String,
    pub name: String,
    pub description: Option<String>,
    pub can_read_sources: Vec<String>,     // ["slack", "git", "manual"]
    pub can_read_tags: Vec<String>,        // ["urgent", "client:enterprise"] 
    pub can_write_tags: Vec<String>,       // ["onboarding", "agent-created"]
    pub can_read_authors: Vec<String>,     // ["slack:*", "git:jane.smith"]
    pub max_memory_age_days: Option<i64>,  // Limit to recent memories
    pub enabled: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AgentQueryRequest {
    pub agent_id: String,
    pub question: String,
    pub max_results: Option<usize>,
    pub min_similarity: Option<f64>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AgentQueryResponse {
    pub agent_id: String,
    pub question: String,
    pub response: String,
    pub memories_used: Vec<PermissionedMemorySnippet>,
    pub total_memories_found: usize,
    pub query_timestamp: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PermissionedMemorySnippet {
    pub id: String,
    pub content: String,
    pub timestamp: DateTime<Utc>,
    pub author: String,
    pub source: String,
    pub tags: Vec<String>,
    pub similarity_score: f64,
    pub provenance: MemoryProvenance,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct MemoryProvenance {
    pub source_type: String,
    pub integration_id: Option<String>,
    pub source_metadata: HashMap<String, serde_json::Value>,
    pub permissions_used: String, // Which agent permission allowed this access
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SourceConfig {
    pub sources: HashMap<String, Source>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Source {
    pub id: String,
    pub name: String,
    pub source_type: String,
    pub config: HashMap<String, serde_json::Value>,
    pub enabled: bool,
}

pub struct Vault {
    pub vault_path: PathBuf,
    pub config: VaultConfig,
    pub index: VaultIndex,
    encryption_key: Option<[u8; 32]>,
}

impl Vault {
    pub fn new(vault_path: PathBuf) -> Result<Self, String> {
        if !vault_path.exists() {
            Self::create_vault_structure(&vault_path)?;
        }

        let config = Self::load_or_create_config(&vault_path)?;
        let index = Self::load_or_create_index(&vault_path)?;

        Ok(Vault {
            vault_path,
            config,
            index,
            encryption_key: None,
        })
    }

    fn create_vault_structure(vault_path: &Path) -> Result<(), String> {
        // Create main vault directory
        fs::create_dir_all(vault_path).map_err(|e| format!("Failed to create vault: {}", e))?;

        // Create subdirectories
        fs::create_dir_all(vault_path.join("config")).map_err(|e| e.to_string())?;
        fs::create_dir_all(vault_path.join("entries")).map_err(|e| e.to_string())?;
        fs::create_dir_all(vault_path.join("encryption")).map_err(|e| e.to_string())?;
        fs::create_dir_all(vault_path.join("metadata")).map_err(|e| e.to_string())?;

        println!("Created vault structure at: {:?}", vault_path);
        Ok(())
    }

    fn load_or_create_config(vault_path: &Path) -> Result<VaultConfig, String> {
        let config_path = vault_path.join("config").join("vault.json");
        
        if config_path.exists() {
            let content = fs::read_to_string(&config_path).map_err(|e| e.to_string())?;
            serde_json::from_str(&content).map_err(|e| e.to_string())
        } else {
            let config = VaultConfig {
                version: "1.0.0".to_string(),
                created_at: Utc::now(),
                encryption_enabled: false,
                default_permissions: EntryPermissions {
                    read: vec!["user:default".to_string()],
                    write: vec!["user:default".to_string()],
                },
                settings: HashMap::new(),
            };
            
            let content = serde_json::to_string_pretty(&config).map_err(|e| e.to_string())?;
            fs::write(&config_path, content).map_err(|e| e.to_string())?;
            
            Ok(config)
        }
    }

    fn load_or_create_index(vault_path: &Path) -> Result<VaultIndex, String> {
        let index_path = vault_path.join("entries").join("index.json");
        
        if index_path.exists() {
            let content = fs::read_to_string(&index_path).map_err(|e| e.to_string())?;
            serde_json::from_str(&content).map_err(|e| e.to_string())
        } else {
            let index = VaultIndex {
                entries: HashMap::new(),
                tags: HashMap::new(),
                last_updated: Utc::now(),
            };
            
            Self::save_index(vault_path, &index)?;
            Ok(index)
        }
    }

    fn save_index(vault_path: &Path, index: &VaultIndex) -> Result<(), String> {
        let index_path = vault_path.join("entries").join("index.json");
        println!("VAULT_SAVE_INDEX: Saving index to: {:?}", index_path);
        println!("VAULT_SAVE_INDEX: Index has {} entries and {} tags", 
                 index.entries.len(), 
                 index.tags.len());
        
        // Ensure the entries directory exists
        let entries_dir = vault_path.join("entries");
        if !entries_dir.exists() {
            fs::create_dir_all(&entries_dir).map_err(|e| {
                let error_msg = format!("Failed to create entries directory {:?}: {}", entries_dir, e);
                println!("VAULT_SAVE_INDEX: {}", error_msg);
                error_msg
            })?;
        }
        
        let content = serde_json::to_string_pretty(index).map_err(|e| {
            let error_msg = format!("Failed to serialize index: {}", e);
            println!("VAULT_SAVE_INDEX: {}", error_msg);
            error_msg
        })?;
        
        fs::write(&index_path, content).map_err(|e| {
            let error_msg = format!("Failed to write index file {:?}: {}", index_path, e);
            println!("VAULT_SAVE_INDEX: {}", error_msg);
            error_msg
        })?;
        
        println!("VAULT_SAVE_INDEX: Successfully saved index");
        Ok(())
    }

    pub fn save_entry(&mut self, entry: VaultEntry) -> Result<(), String> {
        // Create year/month directory structure
        let year_month = entry.timestamp.format("%Y/%m");
        let entries_dir = self.vault_path.join("entries").join(year_month.to_string());
        fs::create_dir_all(&entries_dir).map_err(|e| e.to_string())?;

        // Save entry file
        let entry_filename = format!("entry_{}.json", entry.id);
        let entry_path = entries_dir.join(&entry_filename);
        
        let content = if entry.encryption.encrypted {
            self.encrypt_entry_content(&entry)?
        } else {
            serde_json::to_string_pretty(&entry).map_err(|e| e.to_string())?
        };
        
        fs::write(&entry_path, content).map_err(|e| e.to_string())?;

        // Update index
        let relative_path = format!("{}/{}", year_month, entry_filename);
        let index_info = EntryIndexInfo {
            id: entry.id.clone(),
            timestamp: entry.timestamp,
            author: entry.author.clone(),
            source_type: entry.source.source_type.clone(),
            tags: entry.tags.clone(),
            file_path: relative_path,
        };

        self.index.entries.insert(entry.id.clone(), index_info);
        
        // Update tag index
        for tag in &entry.tags {
            self.index.tags
                .entry(tag.clone())
                .or_insert_with(Vec::new)
                .push(entry.id.clone());
        }

        self.index.last_updated = Utc::now();
        Self::save_index(&self.vault_path, &self.index)?;

        Ok(())
    }

    pub fn get_entry(&self, entry_id: &str) -> Result<Option<VaultEntry>, String> {
        if let Some(index_info) = self.index.entries.get(entry_id) {
            let entry_path = self.vault_path.join("entries").join(&index_info.file_path);
            
            if entry_path.exists() {
                let content = fs::read_to_string(&entry_path).map_err(|e| e.to_string())?;
                
                let entry: VaultEntry = if content.starts_with("encrypted:") {
                    self.decrypt_entry_content(&content)?
                } else {
                    serde_json::from_str(&content).map_err(|e| e.to_string())?
                };
                
                Ok(Some(entry))
            } else {
                Ok(None)
            }
        } else {
            Ok(None)
        }
    }

    pub fn get_all_entries(&self) -> Result<Vec<VaultEntry>, String> {
        let mut entries = Vec::new();
        
        for entry_id in self.index.entries.keys() {
            if let Some(entry) = self.get_entry(entry_id)? {
                entries.push(entry);
            }
        }
        
        // Sort by timestamp (newest first)
        entries.sort_by(|a, b| b.timestamp.cmp(&a.timestamp));
        
        Ok(entries)
    }

    pub fn delete_entry(&mut self, entry_id: &str) -> Result<(), String> {
        println!("VAULT_DELETE: Starting deletion for entry ID: {}", entry_id);
        
        // Check if entry exists in index
        if let Some(index_info) = self.index.entries.get(entry_id) {
            println!("VAULT_DELETE: Found entry in index: {:?}", index_info);
            
            // Build the full file path
            let entry_path = self.vault_path.join("entries").join(&index_info.file_path);
            println!("VAULT_DELETE: Full file path: {:?}", entry_path);
            println!("VAULT_DELETE: File exists: {}", entry_path.exists());
            
            // Remove the physical file first
            if entry_path.exists() {
                match fs::remove_file(&entry_path) {
                    Ok(_) => {
                        println!("VAULT_DELETE: Successfully removed file: {:?}", entry_path);
                    }
                    Err(e) => {
                        let error_msg = format!("Failed to remove file {:?}: {}", entry_path, e);
                        println!("VAULT_DELETE: {}", error_msg);
                        return Err(error_msg);
                    }
                }
            } else {
                println!("VAULT_DELETE: Warning - File not found: {:?}", entry_path);
            }
            
            // Store tag list before removing from index
            let entry_tags = index_info.tags.clone();
            println!("VAULT_DELETE: Entry has tags: {:?}", entry_tags);
            
            // Remove from index
            self.index.entries.remove(entry_id);
            println!("VAULT_DELETE: Removed entry from index");
            
            // Update tag index - clean up tags
            println!("VAULT_DELETE: Cleaning up tags...");
            for tag in &entry_tags {
                if let Some(entry_ids) = self.index.tags.get_mut(tag) {
                    let before_count = entry_ids.len();
                    entry_ids.retain(|id| id != entry_id);
                    let after_count = entry_ids.len();
                    
                    println!("VAULT_DELETE: Tag '{}': {} entries -> {} entries", tag, before_count, after_count);
                    
                    if entry_ids.is_empty() {
                        self.index.tags.remove(tag);
                        println!("VAULT_DELETE: Removed empty tag: {}", tag);
                    }
                }
            }
            
            // Update index timestamp
            self.index.last_updated = Utc::now();
            println!("VAULT_DELETE: Updated index timestamp");
            
            // Save the updated index
            match Self::save_index(&self.vault_path, &self.index) {
                Ok(_) => {
                    println!("VAULT_DELETE: Successfully saved updated index");
                }
                Err(e) => {
                    let error_msg = format!("Failed to save index after deletion: {}", e);
                    println!("VAULT_DELETE: {}", error_msg);
                    return Err(error_msg);
                }
            }
            
            println!("VAULT_DELETE: Successfully deleted entry: {}", entry_id);
            Ok(())
        } else {
            let error_msg = format!("Entry not found in index: {}", entry_id);
            println!("VAULT_DELETE: {}", error_msg);
            Err(error_msg)
        }
    }

    pub fn search_by_tags(&self, tags: &[String]) -> Result<Vec<VaultEntry>, String> {
        let mut matching_ids = Vec::new();
        
        for tag in tags {
            if let Some(entry_ids) = self.index.tags.get(tag) {
                matching_ids.extend(entry_ids.clone());
            }
        }
        
        // Remove duplicates
        matching_ids.sort();
        matching_ids.dedup();
        
        let mut entries = Vec::new();
        for entry_id in matching_ids {
            if let Some(entry) = self.get_entry(&entry_id)? {
                entries.push(entry);
            }
        }
        
        Ok(entries)
    }

    /// Get entries by source type (e.g., "slack", "git", "manual")
    pub fn get_entries_by_source(&self, source_type: &str) -> Result<Vec<VaultEntry>, String> {
        let all_entries = self.get_all_entries()?;
        let filtered_entries = all_entries
            .into_iter()
            .filter(|entry| entry.source.source_type == source_type)
            .collect();
        
        Ok(filtered_entries)
    }

    /// Get all unique source types in the vault
    pub fn get_all_source_types(&self) -> Result<Vec<String>, String> {
        let mut source_types = std::collections::HashSet::new();
        
        for index_info in self.index.entries.values() {
            source_types.insert(index_info.source_type.clone());
        }
        
        let mut result: Vec<String> = source_types.into_iter().collect();
        result.sort();
        Ok(result)
    }

    /// Get all unique tags in the vault
    pub fn get_all_tags(&self) -> Vec<String> {
        let mut tags: Vec<String> = self.index.tags.keys().cloned().collect();
        tags.sort();
        tags
    }

    /// Search entries with multiple filters
    pub fn search_entries(
        &self,
        source_filter: Option<String>,
        tag_filter: Option<Vec<String>>,
        author_filter: Option<String>,
        date_range: Option<(DateTime<Utc>, DateTime<Utc>)>,
    ) -> Result<Vec<VaultEntry>, String> {
        let mut entries = self.get_all_entries()?;

        // Filter by source
        if let Some(source) = source_filter {
            entries.retain(|entry| entry.source.source_type == source);
        }

        // Filter by tags
        if let Some(tags) = tag_filter {
            entries.retain(|entry| {
                tags.iter().any(|tag| entry.tags.contains(tag))
            });
        }

        // Filter by author
        if let Some(author) = author_filter {
            entries.retain(|entry| entry.author.contains(&author));
        }

        // Filter by date range
        if let Some((start, end)) = date_range {
            entries.retain(|entry| entry.timestamp >= start && entry.timestamp <= end);
        }

        Ok(entries)
    }

    fn encrypt_entry_content(&self, entry: &VaultEntry) -> Result<String, String> {
        if let Some(key) = &self.encryption_key {
            let cipher = Aes256Gcm::new(Key::<Aes256Gcm>::from_slice(key));
            let content = serde_json::to_string(entry).map_err(|e| e.to_string())?;
            
            let mut nonce_bytes = [0u8; 12];
            OsRng.fill_bytes(&mut nonce_bytes);
            let nonce = Nonce::from_slice(&nonce_bytes);
            
            let ciphertext = cipher
                .encrypt(nonce, content.as_bytes())
                .map_err(|e| format!("Encryption failed: {}", e))?;
            
            let mut encrypted_data = nonce_bytes.to_vec();
            encrypted_data.extend_from_slice(&ciphertext);
            
            let encoded = general_purpose::STANDARD.encode(encrypted_data);
            Ok(format!("encrypted:{}", encoded))
        } else {
            Err("No encryption key available".to_string())
        }
    }

    fn decrypt_entry_content(&self, encrypted_content: &str) -> Result<VaultEntry, String> {
        if let Some(key) = &self.encryption_key {
            let encoded_data = encrypted_content.strip_prefix("encrypted:")
                .ok_or("Invalid encrypted format")?;
            
            let encrypted_data = general_purpose::STANDARD
                .decode(encoded_data)
                .map_err(|e| format!("Base64 decode failed: {}", e))?;
            
            if encrypted_data.len() < 12 {
                return Err("Invalid encrypted data length".to_string());
            }
            
            let (nonce_bytes, ciphertext) = encrypted_data.split_at(12);
            let nonce = Nonce::from_slice(nonce_bytes);
            
            let cipher = Aes256Gcm::new(Key::<Aes256Gcm>::from_slice(key));
            let plaintext = cipher
                .decrypt(nonce, ciphertext)
                .map_err(|e| format!("Decryption failed: {}", e))?;
            
            let content = String::from_utf8(plaintext)
                .map_err(|e| format!("UTF-8 decode failed: {}", e))?;
            
            serde_json::from_str(&content).map_err(|e| e.to_string())
        } else {
            Err("No encryption key available".to_string())
        }
    }

    // ========== AGENT MANAGEMENT ==========

    /// Load agent permissions from config file
    pub fn load_agent_permissions(&self) -> Result<HashMap<String, AgentPermissions>, String> {
        let agents_path = self.vault_path.join("config").join("agents.json");
        
        if agents_path.exists() {
            let content = fs::read_to_string(&agents_path).map_err(|e| e.to_string())?;
            let agents_data: HashMap<String, AgentPermissions> = serde_json::from_str(&content)
                .map_err(|e| format!("Failed to parse agents.json: {}", e))?;
            Ok(agents_data)
        } else {
            Ok(HashMap::new())
        }
    }

    /// Save agent permissions to config file
    pub fn save_agent_permissions(&self, agents: &HashMap<String, AgentPermissions>) -> Result<(), String> {
        let agents_path = self.vault_path.join("config").join("agents.json");
        let content = serde_json::to_string_pretty(agents).map_err(|e| e.to_string())?;
        fs::write(&agents_path, content).map_err(|e| e.to_string())
    }

    /// Create or update an agent's permissions
    pub fn set_agent_permissions(&mut self, permissions: AgentPermissions) -> Result<(), String> {
        let mut agents = self.load_agent_permissions()?;
        agents.insert(permissions.agent_id.clone(), permissions);
        self.save_agent_permissions(&agents)
    }

    /// Get an agent's permissions
    pub fn get_agent_permissions(&self, agent_id: &str) -> Result<Option<AgentPermissions>, String> {
        let agents = self.load_agent_permissions()?;
        Ok(agents.get(agent_id).cloned())
    }

    /// Get all agents
    pub fn get_all_agents(&self) -> Result<Vec<AgentPermissions>, String> {
        let agents = self.load_agent_permissions()?;
        Ok(agents.into_values().collect())
    }

    /// Delete an agent
    pub fn delete_agent(&mut self, agent_id: &str) -> Result<(), String> {
        let mut agents = self.load_agent_permissions()?;
        agents.remove(agent_id);
        self.save_agent_permissions(&agents)
    }

    /// Filter memories based on agent permissions
    pub fn get_permitted_memories(&self, agent_id: &str) -> Result<Vec<VaultEntry>, String> {
        let permissions = self.get_agent_permissions(agent_id)?
            .ok_or_else(|| format!("Agent '{}' not found", agent_id))?;

        if !permissions.enabled {
            return Ok(Vec::new());
        }

        let all_entries = self.get_all_entries()?;
        let mut permitted_entries = Vec::new();

        for entry in all_entries {
            if self.is_memory_permitted(&entry, &permissions)? {
                permitted_entries.push(entry);
            }
        }

        Ok(permitted_entries)
    }

    /// Check if a memory entry is permitted for an agent
    fn is_memory_permitted(&self, entry: &VaultEntry, permissions: &AgentPermissions) -> Result<bool, String> {
        // Check if agent is enabled
        if !permissions.enabled {
            return Ok(false);
        }

        // Check source permissions
        if !permissions.can_read_sources.is_empty() && 
           !permissions.can_read_sources.contains(&entry.source.source_type) {
            return Ok(false);
        }

        // Check tag permissions (if entry has any of the allowed tags)
        if !permissions.can_read_tags.is_empty() {
            let has_allowed_tag = entry.tags.iter()
                .any(|tag| permissions.can_read_tags.iter()
                    .any(|allowed| tag == allowed || tag.starts_with(&format!("{}:", allowed))));
            
            if !has_allowed_tag {
                return Ok(false);
            }
        }

        // Check author permissions
        if !permissions.can_read_authors.is_empty() {
            let author_allowed = permissions.can_read_authors.iter()
                .any(|allowed_author| {
                    if allowed_author.ends_with("*") {
                        let prefix = &allowed_author[..allowed_author.len()-1];
                        entry.author.starts_with(prefix)
                    } else {
                        entry.author == *allowed_author
                    }
                });
            
            if !author_allowed {
                return Ok(false);
            }
        }

        // Check memory age limit
        if let Some(max_age_days) = permissions.max_memory_age_days {
            let cutoff_date = Utc::now() - chrono::Duration::days(max_age_days);
            if entry.timestamp < cutoff_date {
                return Ok(false);
            }
        }

        Ok(true)
    }
}

impl VaultEntry {
    pub fn new(content: String, author: String, source_type: String) -> Self {
        VaultEntry {
            id: Uuid::new_v4().to_string(),
            content,
            timestamp: Utc::now(),
            author: author.clone(),
            source: EntrySource {
                source_type,
                integration_id: None,
                metadata: HashMap::new(),
            },
            tags: Vec::new(),
            permissions: EntryPermissions {
                read: vec![format!("user:{}", author)],
                write: vec![format!("user:{}", author)],
            },
            encryption: EncryptionInfo {
                encrypted: false,
                key_id: None,
            },
            metadata: EntryMetadata {
                location: None,
                priority: None,
                related_entries: Vec::new(),
                embedding_vector: None,
                custom: HashMap::new(),
            },
        }
    }

    pub fn with_tags(mut self, tags: Vec<String>) -> Self {
        self.tags = tags;
        self
    }

    pub fn with_embedding(mut self, embedding: Vec<f64>) -> Self {
        self.metadata.embedding_vector = Some(embedding);
        self
    }

    /// Create a new VaultEntry from external source data
    pub fn from_external_source(
        content: String,
        source_type: String,
        author: String,
        integration_id: Option<String>,
        source_metadata: HashMap<String, serde_json::Value>,
        custom_tags: Vec<String>,
    ) -> Self {
        let mut entry = VaultEntry {
            id: Uuid::new_v4().to_string(),
            content,
            timestamp: Utc::now(),
            author: author.clone(),
            source: EntrySource {
                source_type: source_type.clone(),
                integration_id,
                metadata: source_metadata,
            },
            tags: custom_tags,
            permissions: EntryPermissions {
                read: vec![format!("user:{}", author), "agent:*".to_string()],
                write: vec![format!("user:{}", author)],
            },
            encryption: EncryptionInfo {
                encrypted: false,
                key_id: None,
            },
            metadata: EntryMetadata {
                location: None,
                priority: None,
                related_entries: Vec::new(),
                embedding_vector: None,
                custom: HashMap::new(),
            },
        };

        // Auto-add source type as tag
        entry.tags.push(source_type);
        entry
    }

    /// Create a Slack memory entry with proper formatting and tagging
    pub fn from_slack(
        text: String,
        channel: String,
        author: String,
        timestamp: Option<DateTime<Utc>>,
        thread_ts: Option<String>,
        message_type: Option<String>,
    ) -> Self {
        let mut source_metadata = HashMap::new();
        source_metadata.insert("channel".to_string(), serde_json::Value::String(channel.clone()));
        
        if let Some(thread) = thread_ts {
            source_metadata.insert("thread_ts".to_string(), serde_json::Value::String(thread));
        }
        
        if let Some(msg_type) = message_type {
            source_metadata.insert("message_type".to_string(), serde_json::Value::String(msg_type));
        }

        let tags = vec![
            "slack".to_string(),
            format!("channel:{}", channel),
        ];

        let mut entry = VaultEntry::from_external_source(
            text,
            "slack".to_string(),
            format!("slack:{}", author),
            Some(format!("slack-{}", channel)),
            source_metadata,
            tags,
        );

        if let Some(ts) = timestamp {
            entry.timestamp = ts;
        }

        entry
    }

    /// Create a Git commit memory entry
    pub fn from_git_commit(
        commit_message: String,
        author: String,
        repo: String,
        branch: String,
        commit_hash: String,
        timestamp: Option<DateTime<Utc>>,
    ) -> Self {
        let mut source_metadata = HashMap::new();
        source_metadata.insert("repo".to_string(), serde_json::Value::String(repo.clone()));
        source_metadata.insert("branch".to_string(), serde_json::Value::String(branch.clone()));
        source_metadata.insert("commit_hash".to_string(), serde_json::Value::String(commit_hash));

        let tags = vec![
            "git".to_string(),
            format!("repo:{}", repo),
            format!("branch:{}", branch),
        ];

        let mut entry = VaultEntry::from_external_source(
            commit_message,
            "git".to_string(),
            format!("git:{}", author),
            Some(format!("git-{}", repo)),
            source_metadata,
            tags,
        );

        if let Some(ts) = timestamp {
            entry.timestamp = ts;
        }

        entry
    }

    pub fn with_custom_metadata(mut self, key: String, value: serde_json::Value) -> Self {
        self.metadata.custom.insert(key, value);
        self
    }

    pub fn with_priority(mut self, priority: String) -> Self {
        self.metadata.priority = Some(priority);
        self
    }
} 
// Phase 3: Modular Plugin Architecture
use std::collections::HashMap;
use serde::{Deserialize, Serialize};
use async_trait::async_trait;
use crate::vault::VaultEntry;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PluginMetadata {
    pub id: String,
    pub name: String,
    pub version: String,
    pub description: String,
    pub supported_formats: Vec<String>,
}

#[async_trait]
pub trait IngestionPlugin: Send + Sync {
    fn metadata(&self) -> PluginMetadata;
    async fn ingest(&self, config: PluginConfig) -> Result<Vec<VaultEntry>, String>;
    fn validate_config(&self, config: &PluginConfig) -> Result<(), String>;
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PluginConfig {
    pub plugin_id: String,
    pub parameters: HashMap<String, serde_json::Value>,
}

pub struct PluginManager {
    plugins: HashMap<String, Box<dyn IngestionPlugin>>,
}

impl PluginManager {
    pub fn new() -> Self {
        PluginManager { plugins: HashMap::new() }
    }

    pub fn register_plugin(&mut self, plugin: Box<dyn IngestionPlugin>) {
        let metadata = plugin.metadata();
        self.plugins.insert(metadata.id.clone(), plugin);
    }

    pub async fn execute_ingestion(&self, config: PluginConfig) -> Result<Vec<VaultEntry>, String> {
        let plugin = self.plugins.get(&config.plugin_id)
            .ok_or("Plugin not found")?;
        plugin.validate_config(&config)?;
        plugin.ingest(config).await
    }
}

use std::collections::HashMap;
use std::path::PathBuf;
use std::fs;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use crate::app_integrations::AppType;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PermissionRequest {
    pub id: String,
    pub agent_id: String,
    pub agent_name: String,
    pub app_type: AppType,
    pub requested_permissions: Vec<String>,
    pub justification: String,
    pub status: RequestStatus,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub expires_at: DateTime<Utc>,
    pub user_response: Option<UserResponse>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RequestStatus {
    Pending,
    Approved,
    Denied,
    Expired,
    Cancelled,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserResponse {
    pub decision: Decision,
    pub granted_permissions: Vec<String>, // May be subset of requested
    pub conditions: Vec<String>, // e.g., "Only weekday emails", "No sensitive data"
    pub expiry_date: Option<DateTime<Utc>>, // Temporary access
    pub notes: Option<String>,
    pub responded_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum Decision {
    Approve,
    Deny,
    ApproveWithConditions,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PermissionTemplate {
    pub app_type: AppType,
    pub permission_name: String,
    pub description: String,
    pub risk_level: RiskLevel,
    pub data_types: Vec<String>,
    pub example_use_cases: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RiskLevel {
    Low,    // Read-only, non-sensitive data
    Medium, // Personal data, limited write access
    High,   // Sensitive data, full access
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NotificationSettings {
    pub email_notifications: bool,
    pub desktop_notifications: bool,
    pub auto_approve_low_risk: bool,
    pub auto_deny_high_risk: bool,
    pub require_justification: bool,
}

pub struct PermissionRequestManager {
    vault_path: PathBuf,
    pending_requests: HashMap<String, PermissionRequest>,
    request_history: Vec<PermissionRequest>,
    permission_templates: HashMap<String, PermissionTemplate>,
    notification_settings: NotificationSettings,
}

impl PermissionRequestManager {
    pub fn new(vault_path: PathBuf) -> Result<Self, String> {
        let requests_path = vault_path.join("config").join("permission_requests.json");
        let templates_path = vault_path.join("config").join("permission_templates.json");
        let settings_path = vault_path.join("config").join("notification_settings.json");

        let (pending_requests, request_history) = if requests_path.exists() {
            let content = fs::read_to_string(&requests_path).map_err(|e| e.to_string())?;
            let all_requests: Vec<PermissionRequest> = serde_json::from_str(&content).unwrap_or_default();
            
            let mut pending = HashMap::new();
            let mut history = Vec::new();
            
            for request in all_requests {
                if matches!(request.status, RequestStatus::Pending) {
                    pending.insert(request.id.clone(), request);
                } else {
                    history.push(request);
                }
            }
            
            (pending, history)
        } else {
            (HashMap::new(), Vec::new())
        };

        let permission_templates = if templates_path.exists() {
            let content = fs::read_to_string(&templates_path).map_err(|e| e.to_string())?;
            serde_json::from_str(&content).unwrap_or_else(|_| Self::default_permission_templates())
        } else {
            Self::default_permission_templates()
        };

        let notification_settings = if settings_path.exists() {
            let content = fs::read_to_string(&settings_path).map_err(|e| e.to_string())?;
            serde_json::from_str(&content).unwrap_or_default()
        } else {
            NotificationSettings::default()
        };

        Ok(PermissionRequestManager {
            vault_path,
            pending_requests,
            request_history,
            permission_templates,
            notification_settings,
        })
    }

    pub fn create_permission_request(
        &mut self,
        agent_id: String,
        agent_name: String,
        app_type: AppType,
        requested_permissions: Vec<String>,
        justification: String,
    ) -> Result<String, String> {
        let request_id = uuid::Uuid::new_v4().to_string();
        
        let request = PermissionRequest {
            id: request_id.clone(),
            agent_id,
            agent_name,
            app_type,
            requested_permissions,
            justification,
            status: RequestStatus::Pending,
            created_at: Utc::now(),
            updated_at: Utc::now(),
            expires_at: Utc::now() + chrono::Duration::days(7), // 7 day expiry
            user_response: None,
        };

        // Check if auto-approval/denial applies
        if self.should_auto_process(&request) {
            let auto_response = self.generate_auto_response(&request);
            let mut request = request;
            request.user_response = Some(auto_response.clone());
            request.status = match auto_response.decision {
                Decision::Approve | Decision::ApproveWithConditions => RequestStatus::Approved,
                Decision::Deny => RequestStatus::Denied,
            };
            request.updated_at = Utc::now();
            
            self.request_history.push(request);
        } else {
            self.pending_requests.insert(request_id.clone(), request);
        }

        self.save_requests()?;
        Ok(request_id)
    }

    pub fn get_pending_requests(&self) -> Vec<&PermissionRequest> {
        self.pending_requests.values().collect()
    }

    pub fn get_request(&self, request_id: &str) -> Option<&PermissionRequest> {
        self.pending_requests.get(request_id)
            .or_else(|| self.request_history.iter().find(|r| r.id == request_id))
    }

    pub fn respond_to_request(
        &mut self,
        request_id: &str,
        decision: Decision,
        granted_permissions: Option<Vec<String>>,
        conditions: Option<Vec<String>>,
        expiry_date: Option<DateTime<Utc>>,
        notes: Option<String>,
    ) -> Result<(), String> {
        let mut request = self.pending_requests.remove(request_id)
            .ok_or("Request not found or already processed")?;

        let user_response = UserResponse {
            decision: decision.clone(),
            granted_permissions: granted_permissions.unwrap_or(request.requested_permissions.clone()),
            conditions: conditions.unwrap_or_default(),
            expiry_date,
            notes,
            responded_at: Utc::now(),
        };

        request.user_response = Some(user_response);
        request.status = match decision {
            Decision::Approve | Decision::ApproveWithConditions => RequestStatus::Approved,
            Decision::Deny => RequestStatus::Denied,
        };
        request.updated_at = Utc::now();

        self.request_history.push(request);
        self.save_requests()?;
        Ok(())
    }

    pub fn cancel_request(&mut self, request_id: &str) -> Result<(), String> {
        if let Some(mut request) = self.pending_requests.remove(request_id) {
            request.status = RequestStatus::Cancelled;
            request.updated_at = Utc::now();
            self.request_history.push(request);
            self.save_requests()?;
            Ok(())
        } else {
            Err("Request not found or already processed".to_string())
        }
    }

    pub fn cleanup_expired_requests(&mut self) -> Result<usize, String> {
        let now = Utc::now();
        let mut expired_count = 0;

        let expired_requests: Vec<_> = self.pending_requests
            .iter()
            .filter(|(_, request)| request.expires_at < now)
            .map(|(id, _)| id.clone())
            .collect();

        for request_id in expired_requests {
            if let Some(mut request) = self.pending_requests.remove(&request_id) {
                request.status = RequestStatus::Expired;
                request.updated_at = now;
                self.request_history.push(request);
                expired_count += 1;
            }
        }

        if expired_count > 0 {
            self.save_requests()?;
        }

        Ok(expired_count)
    }

    pub fn get_permission_templates(&self) -> &HashMap<String, PermissionTemplate> {
        &self.permission_templates
    }

    pub fn get_agent_permissions_history(&self, agent_id: &str) -> Vec<&PermissionRequest> {
        self.request_history
            .iter()
            .filter(|request| request.agent_id == agent_id)
            .collect()
    }

    fn should_auto_process(&self, request: &PermissionRequest) -> bool {
        let risk_level = self.calculate_risk_level(&request.requested_permissions, &request.app_type);
        
        match risk_level {
            RiskLevel::Low => self.notification_settings.auto_approve_low_risk,
            RiskLevel::High => self.notification_settings.auto_deny_high_risk,
            RiskLevel::Medium => false,
        }
    }

    fn generate_auto_response(&self, request: &PermissionRequest) -> UserResponse {
        let risk_level = self.calculate_risk_level(&request.requested_permissions, &request.app_type);
        
        match risk_level {
            RiskLevel::Low => UserResponse {
                decision: Decision::Approve,
                granted_permissions: request.requested_permissions.clone(),
                conditions: vec!["Auto-approved for low-risk permissions".to_string()],
                expiry_date: Some(Utc::now() + chrono::Duration::days(30)),
                notes: Some("Automatically approved based on low risk assessment".to_string()),
                responded_at: Utc::now(),
            },
            RiskLevel::High => UserResponse {
                decision: Decision::Deny,
                granted_permissions: Vec::new(),
                conditions: Vec::new(),
                expiry_date: None,
                notes: Some("Automatically denied due to high risk assessment".to_string()),
                responded_at: Utc::now(),
            },
            RiskLevel::Medium => unreachable!("Medium risk should not be auto-processed"),
        }
    }

    fn calculate_risk_level(&self, permissions: &[String], _app_type: &AppType) -> RiskLevel {
        // Simple risk calculation - in production this would be more sophisticated
        let high_risk_keywords = ["write", "delete", "admin", "sensitive", "private"];
        let has_high_risk = permissions.iter().any(|p| 
            high_risk_keywords.iter().any(|keyword| p.to_lowercase().contains(keyword))
        );

        if has_high_risk {
            RiskLevel::High
        } else if permissions.len() > 3 {
            RiskLevel::Medium
        } else {
            RiskLevel::Low
        }
    }

    fn default_permission_templates() -> HashMap<String, PermissionTemplate> {
        let mut templates = HashMap::new();
        
        // Add default templates for common permissions
        templates.insert("gmail_read".to_string(), PermissionTemplate {
            app_type: AppType::Gmail,
            permission_name: "Read Emails".to_string(),
            description: "Read email messages and metadata".to_string(),
            risk_level: RiskLevel::Medium,
            data_types: vec!["Email content".to_string(), "Sender/recipient info".to_string()],
            example_use_cases: vec!["Summarize important emails".to_string(), "Track project updates".to_string()],
        });

        templates
    }

    fn save_requests(&self) -> Result<(), String> {
        let requests_path = self.vault_path.join("config").join("permission_requests.json");
        
        let mut all_requests = self.request_history.clone();
        all_requests.extend(self.pending_requests.values().cloned());
        
        let content = serde_json::to_string_pretty(&all_requests).map_err(|e| e.to_string())?;
        fs::write(&requests_path, content).map_err(|e| e.to_string())
    }
}

impl Default for NotificationSettings {
    fn default() -> Self {
        NotificationSettings {
            email_notifications: true,
            desktop_notifications: true,
            auto_approve_low_risk: false,
            auto_deny_high_risk: false,
            require_justification: true,
        }
    }
}

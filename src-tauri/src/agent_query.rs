use crate::vault::{<PERSON><PERSON>, AgentQueryRequest, AgentQueryResponse, PermissionedMemorySnippet, MemoryProvenance, VaultEntry};
use chrono::Utc;
use serde_json::json;

pub async fn execute_agent_query(
    vault: &Vault,
    request: AgentQueryRequest,
) -> Result<AgentQueryResponse, String> {
    // 1. Load agent permissions and permitted memories
    let agent_permissions = vault.get_agent_permissions(&request.agent_id)?
        .ok_or_else(|| format!("Agent '{}' not found", request.agent_id))?;
    if !agent_permissions.enabled {
        return Err("Agent is disabled".to_string());
    }
    let mut permitted_memories = vault.get_permitted_memories(&request.agent_id)?;
    // 2. Sort by recency (timestamp desc)
    permitted_memories.sort_by(|a, b| b.timestamp.cmp(&a.timestamp));
    let max_results = request.max_results.unwrap_or(5);
    let selected_memories: Vec<&VaultEntry> = permitted_memories.iter().take(max_results).collect();
    // 3. Format prompt
    let agent_name = agent_permissions.name.clone();
    let mut prompt = format!(
        "You are an AI agent named {}. Based on these memories, answer this question:\n\n",
        agent_name
    );
    for (i, mem) in selected_memories.iter().enumerate() {
        prompt.push_str(&format!("Memory {}: \"{}\"\n", i + 1, mem.content));
    }
    prompt.push_str(&format!("Question: {}", request.question));
    // 4. Call Ollama
    let ollama_url = "http://localhost:11434/api/generate";
    let client = reqwest::Client::new();
    let body = json!({
        "model": "llama3",
        "prompt": prompt,
        "stream": false
    });
    let resp = client.post(ollama_url)
        .json(&body)
        .send()
        .await
        .map_err(|e| format!("Failed to call Ollama: {}", e))?;
    let resp_json: serde_json::Value = resp.json().await.map_err(|e| format!("Invalid Ollama response: {}", e))?;
    let answer = resp_json["response"].as_str().unwrap_or("").to_string();
    // 5. Build PermissionedMemorySnippet list
    let memories_used: Vec<PermissionedMemorySnippet> = selected_memories.iter().map(|mem| {
        PermissionedMemorySnippet {
            id: mem.id.clone(),
            content: mem.content.clone(),
            timestamp: mem.timestamp,
            author: mem.author.clone(),
            source: mem.source.source_type.clone(),
            tags: mem.tags.clone(),
            similarity_score: 1.0, // recency only for now
            provenance: MemoryProvenance {
                source_type: mem.source.source_type.clone(),
                integration_id: mem.source.integration_id.clone(),
                source_metadata: mem.source.metadata.clone(),
                permissions_used: "recency".to_string(),
            },
        }
    }).collect();
    Ok(AgentQueryResponse {
        agent_id: request.agent_id,
        question: request.question,
        response: answer,
        memories_used,
        total_memories_found: permitted_memories.len(),
        query_timestamp: Utc::now(),
    })
} 
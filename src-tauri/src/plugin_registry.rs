// Phase 5: Plugin Registry System
use std::collections::HashMap;
use std::path::PathBuf;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PluginManifest {
    pub id: String,
    pub name: String,
    pub version: String,
    pub description: String,
    pub author: String,
    pub license: String,
    pub mindmesh_version: String,
    pub plugin_type: PluginType,
    pub capabilities: PluginCapabilities,
    pub permissions: PluginPermissions,
    pub entry_point: String,
    pub signatures: PluginSignatures,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PluginType {
    MemorySource,
    AgentBehavior,
    UIComponent,
    DataProcessor,
    Integration,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PluginCapabilities {
    pub can_read_memories: bool,
    pub can_write_memories: bool,
    pub can_access_network: bool,
    pub can_execute_code: bool,
    pub can_modify_agents: bool,
    pub required_apis: Vec<String>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct PluginPermissions {
    pub memory_sources: Vec<String>,
    pub agent_access: Vec<String>,
    pub file_system_access: bool,
    pub network_domains: Vec<String>,
    pub sensitive_data_access: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PluginSignatures {
    pub author_signature: String,
    pub code_hash: String,
    pub mindmesh_verified: bool,
    pub community_rating: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InstalledPlugin {
    pub manifest: PluginManifest,
    pub installation_path: PathBuf,
    pub installed_at: DateTime<Utc>,
    pub enabled: bool,
}

pub struct PluginRegistry {
    registry_path: PathBuf,
    pub installed_plugins: HashMap<String, InstalledPlugin>,
}

impl PluginRegistry {
    pub fn new(registry_path: PathBuf) -> Result<Self, String> {
        Ok(PluginRegistry {
            registry_path,
            installed_plugins: HashMap::new(),
        })
    }

    pub async fn install_plugin(&mut self, manifest: PluginManifest, plugin_data: Vec<u8>) -> Result<String, String> {
        let install_path = self.registry_path.join("plugins").join(&manifest.id);
        std::fs::create_dir_all(&install_path).map_err(|e| e.to_string())?;

        let installed_plugin = InstalledPlugin {
            manifest: manifest.clone(),
            installation_path: install_path,
            installed_at: Utc::now(),
            enabled: true,
        };

        self.installed_plugins.insert(manifest.id.clone(), installed_plugin);
        Ok(format!("Plugin {} installed successfully", manifest.name))
    }
}

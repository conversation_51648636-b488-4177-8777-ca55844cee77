// Phase 5: Plugin Validation & Security Checks
use serde::{Deserialize, Serialize};
use sha3::{Digest, Sha3_256};
use hex;
use crate::plugin_registry::PluginManifest;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ValidationResult {
    pub is_valid: bool,
    pub security_score: u32,
    pub warnings: Vec<ValidationWarning>,
    pub errors: Vec<ValidationError>,
    pub recommendations: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationWarning {
    pub code: String,
    pub message: String,
    pub severity: WarningSeverity,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ValidationError {
    pub code: String,
    pub message: String,
    pub blocking: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum WarningSeverity {
    Low,
    Medium,
    High,
    Critical,
}

pub struct PluginValidator {
    trusted_authors: Vec<String>,
}

impl PluginValidator {
    pub fn new() -> Self {
        PluginValidator {
            trusted_authors: vec![
                "mindmesh-official".to_string(),
                "verified-developers".to_string(),
            ],
        }
    }

    pub fn validate_plugin(
        &self,
        manifest: &PluginManifest,
        plugin_code: &[u8],
    ) -> Result<ValidationResult, String> {
        let mut warnings = Vec::new();
        let mut errors = Vec::new();
        let mut recommendations = Vec::new();
        let mut security_score = 100u32;

        // Validate manifest structure
        if manifest.id.is_empty() || manifest.name.is_empty() {
            errors.push(ValidationError {
                code: "MISSING_REQUIRED_FIELDS".to_string(),
                message: "Plugin ID and name are required".to_string(),
                blocking: true,
            });
        }

        // Check author trust
        if !self.trusted_authors.contains(&manifest.author) && !manifest.signatures.mindmesh_verified {
            warnings.push(ValidationWarning {
                code: "UNTRUSTED_AUTHOR".to_string(),
                message: "Plugin from unverified author".to_string(),
                severity: WarningSeverity::High,
            });
            security_score -= 20;
        }

        // Verify code hash
        let mut hasher = Sha3_256::new();
        hasher.update(plugin_code);
        let calculated_hash = hex::encode(hasher.finalize());

        if calculated_hash != manifest.signatures.code_hash {
            errors.push(ValidationError {
                code: "HASH_MISMATCH".to_string(),
                message: "Plugin code hash does not match manifest".to_string(),
                blocking: true,
            });
            security_score = 0;
        }

        // Check permissions
        if manifest.permissions.sensitive_data_access && !manifest.signatures.mindmesh_verified {
            warnings.push(ValidationWarning {
                code: "UNVERIFIED_SENSITIVE_ACCESS".to_string(),
                message: "Unverified plugin requesting sensitive data access".to_string(),
                severity: WarningSeverity::Critical,
            });
            security_score -= 30;
        }

        // Generate recommendations
        if security_score < 70 {
            recommendations.push("Consider reducing requested permissions".to_string());
        }
        if !manifest.signatures.mindmesh_verified {
            recommendations.push("Consider getting MindMesh verification for increased trust".to_string());
        }

        let is_valid = errors.is_empty() && security_score >= 50;

        Ok(ValidationResult {
            is_valid,
            security_score,
            warnings,
            errors,
            recommendations,
        })
    }
}

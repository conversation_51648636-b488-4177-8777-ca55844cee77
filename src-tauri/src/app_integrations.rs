use std::collections::HashMap;
use std::path::PathBuf;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppIntegration {
    pub id: String,
    pub app_type: AppType,
    pub name: String,
    pub description: String,
    pub system_permissions: SystemPermissions,
    pub connection_status: ConnectionStatus,
    pub data_sources: Vec<DataSource>,
    pub last_sync: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum AppType {
    Notion,
    Gmail,
    GoogleCalendar,
    Slack,
    GitHub,
    Linear,
    Figma,
    Trello,
    Asana,
    Custom(String),
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SystemPermissions {
    pub file_system_access: bool,
    pub network_access: bool,
    pub process_access: bool,
    pub requested_paths: Vec<PathBuf>,
    pub granted_paths: Vec<PathBuf>,
    pub permission_level: PermissionLevel,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum PermissionLevel {
    ReadOnly,
    ReadWrite,
    FullAccess,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataSource {
    pub source_type: String,
    pub path: PathBuf,
    pub file_patterns: Vec<String>,
    pub enabled: bool,
    pub last_accessed: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ConnectionStatus {
    NotConfigured,
    PermissionRequested,
    PermissionGranted,
    PermissionDenied,
    Connected,
    Error(String),
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppConnection {
    pub integration_id: String,
    pub access_token: String, // Encrypted
    pub refresh_token: Option<String>, // Encrypted
    pub token_expires_at: Option<DateTime<Utc>>,
    pub user_info: HashMap<String, serde_json::Value>,
    pub connected_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SyncConfig {
    pub enabled: bool,
    pub sync_interval_minutes: u32,
    pub last_sync_cursor: Option<String>,
    pub filters: SyncFilters,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SyncFilters {
    pub date_range: Option<(DateTime<Utc>, DateTime<Utc>)>,
    pub include_patterns: Vec<String>,
    pub exclude_patterns: Vec<String>,
    pub max_items_per_sync: Option<u32>,
}

impl AppType {
    pub fn default_system_permissions(&self) -> SystemPermissions {
        match self {
            AppType::Notion => SystemPermissions {
                file_system_access: true,
                network_access: false,
                process_access: false,
                requested_paths: vec![
                    PathBuf::from("~/Library/Application Support/Notion"),
                    PathBuf::from("~/Documents/Notion"),
                ],
                granted_paths: vec![],
                permission_level: PermissionLevel::ReadOnly,
            },
            AppType::Gmail => SystemPermissions {
                file_system_access: true,
                network_access: true,
                process_access: false,
                requested_paths: vec![
                    PathBuf::from("~/Library/Mail"),
                    PathBuf::from("~/Library/Application Support/Google/Chrome/Default/Mail"),
                ],
                granted_paths: vec![],
                permission_level: PermissionLevel::ReadOnly,
            },
            AppType::GoogleCalendar => SystemPermissions {
                file_system_access: true,
                network_access: true,
                process_access: false,
                requested_paths: vec![
                    PathBuf::from("~/Library/Calendars"),
                    PathBuf::from("~/Library/Application Support/Google/Chrome/Default/Calendar"),
                ],
                granted_paths: vec![],
                permission_level: PermissionLevel::ReadOnly,
            },
            AppType::Slack => SystemPermissions {
                file_system_access: true,
                network_access: false,
                process_access: true,
                requested_paths: vec![
                    PathBuf::from("~/Library/Application Support/Slack"),
                    PathBuf::from("~/Library/Logs/Slack"),
                ],
                granted_paths: vec![],
                permission_level: PermissionLevel::ReadOnly,
            },
            AppType::GitHub => SystemPermissions {
                file_system_access: true,
                network_access: false,
                process_access: false,
                requested_paths: vec![
                    PathBuf::from("~/Documents/GitHub"),
                    PathBuf::from("~/.git"),
                    PathBuf::from("~/Projects"),
                ],
                granted_paths: vec![],
                permission_level: PermissionLevel::ReadOnly,
            },
            _ => SystemPermissions {
                file_system_access: false,
                network_access: false,
                process_access: false,
                requested_paths: vec![],
                granted_paths: vec![],
                permission_level: PermissionLevel::ReadOnly,
            },
        }
    }

    pub fn get_data_sources(&self) -> Vec<DataSource> {
        match self {
            AppType::Notion => vec![
                DataSource {
                    source_type: "notion_pages".to_string(),
                    path: PathBuf::from("~/Library/Application Support/Notion"),
                    file_patterns: vec!["*.json".to_string(), "*.md".to_string()],
                    enabled: true,
                    last_accessed: None,
                },
            ],
            AppType::Gmail => vec![
                DataSource {
                    source_type: "email_messages".to_string(),
                    path: PathBuf::from("~/Library/Mail"),
                    file_patterns: vec!["*.emlx".to_string(), "*.eml".to_string()],
                    enabled: true,
                    last_accessed: None,
                },
            ],
            AppType::Slack => vec![
                DataSource {
                    source_type: "slack_messages".to_string(),
                    path: PathBuf::from("~/Library/Application Support/Slack"),
                    file_patterns: vec!["*.json".to_string(), "*.log".to_string()],
                    enabled: true,
                    last_accessed: None,
                },
            ],
            AppType::GitHub => vec![
                DataSource {
                    source_type: "git_repositories".to_string(),
                    path: PathBuf::from("~/Documents/GitHub"),
                    file_patterns: vec!["*.md".to_string(), "*.txt".to_string(), "*.json".to_string()],
                    enabled: true,
                    last_accessed: None,
                },
            ],
            _ => vec![],
        }
    }

    pub fn display_name(&self) -> &str {
        match self {
            AppType::Notion => "Notion",
            AppType::Gmail => "Gmail",
            AppType::GoogleCalendar => "Google Calendar",
            AppType::Slack => "Slack",
            AppType::GitHub => "GitHub",
            AppType::Linear => "Linear",
            AppType::Figma => "Figma",
            AppType::Trello => "Trello",
            AppType::Asana => "Asana",
            AppType::Custom(name) => name,
        }
    }

    pub fn icon(&self) -> &str {
        match self {
            AppType::Notion => "📝",
            AppType::Gmail => "📧",
            AppType::GoogleCalendar => "📅",
            AppType::Slack => "💬",
            AppType::GitHub => "🐙",
            AppType::Linear => "📋",
            AppType::Figma => "🎨",
            AppType::Trello => "📊",
            AppType::Asana => "✅",
            AppType::Custom(_) => "🔗",
        }
    }
}

impl AppIntegration {
    pub fn new(app_type: AppType, name: String, description: String) -> Self {
        let system_permissions = app_type.default_system_permissions();
        let data_sources = app_type.get_data_sources();

        AppIntegration {
            id: uuid::Uuid::new_v4().to_string(),
            app_type,
            name,
            description,
            system_permissions,
            connection_status: ConnectionStatus::NotConfigured,
            data_sources,
            last_sync: None,
            created_at: Utc::now(),
            updated_at: Utc::now(),
        }
    }

    pub fn request_system_permissions(&mut self) -> Vec<PathBuf> {
        self.connection_status = ConnectionStatus::PermissionRequested;
        self.updated_at = Utc::now();
        self.system_permissions.requested_paths.clone()
    }

    pub fn grant_permissions(&mut self, granted_paths: Vec<PathBuf>) {
        self.system_permissions.granted_paths = granted_paths;
        self.connection_status = ConnectionStatus::PermissionGranted;
        self.updated_at = Utc::now();
    }

    pub fn deny_permissions(&mut self) {
        self.connection_status = ConnectionStatus::PermissionDenied;
        self.updated_at = Utc::now();
    }

    pub fn update_connection_status(&mut self, status: ConnectionStatus) {
        self.connection_status = status;
        self.updated_at = Utc::now();
    }

    pub fn can_access_path(&self, path: &PathBuf) -> bool {
        self.system_permissions.granted_paths.iter().any(|granted_path| {
            path.starts_with(granted_path)
        })
    }
}

impl Default for SyncConfig {
    fn default() -> Self {
        SyncConfig {
            enabled: true,
            sync_interval_minutes: 15, // Sync every 15 minutes
            last_sync_cursor: None,
            filters: SyncFilters {
                date_range: None,
                include_patterns: Vec::new(),
                exclude_patterns: Vec::new(),
                max_items_per_sync: Some(100),
            },
        }
    }
}

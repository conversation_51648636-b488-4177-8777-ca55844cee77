// Phase 5: Plugin System API Commands

// Plugin Registry Commands
plugin_registry_install(manifest: PluginManifest, plugin_data: Vec<u8>) -> Result<String, String>
plugin_registry_uninstall(plugin_id: String) -> Result<String, String>
plugin_registry_list_installed() -> Result<Vec<InstalledPlugin>, String>
plugin_registry_enable(plugin_id: String) -> Result<String, String>
plugin_registry_disable(plugin_id: String) -> Result<String, String>
plugin_registry_get_details(plugin_id: String) -> Result<InstalledPlugin, String>

// Plugin Validation Commands
plugin_validator_validate(manifest: PluginManifest, plugin_code: Vec<u8>) -> Result<ValidationResult, String>
plugin_validator_check_permissions(plugin_id: String) -> Result<PluginPermissions, String>
plugin_validator_scan_security(plugin_id: String) -> Result<SecurityReport, String>

// Plugin Sandbox Commands
plugin_sandbox_execute(plugin_id: String, input_data: String) -> Result<PluginExecutionResult, String>
plugin_sandbox_get_status(plugin_id: String) -> Result<SandboxStatus, String>
plugin_sandbox_kill_process(plugin_id: String) -> Result<String, String>
plugin_sandbox_get_resource_usage(plugin_id: String) -> Result<ResourceUsage, String>

// Plugin Marketplace Commands
plugin_marketplace_search(query: PluginSearchQuery) -> Result<PluginSearchResult, String>
plugin_marketplace_get_plugin(plugin_id: String) -> Result<MarketplacePlugin, String>
plugin_marketplace_download(plugin_id: String) -> Result<Vec<u8>, String>
plugin_marketplace_publish(request: PluginPublishRequest) -> Result<String, String>
plugin_marketplace_get_my_plugins() -> Result<Vec<MarketplacePlugin>, String>
plugin_marketplace_update_plugin(plugin_id: String, update_data: PluginUpdateRequest) -> Result<String, String>

// Plugin Integration Commands
plugin_system_hook_memory_ingestion(plugin_id: String, source_type: String) -> Result<String, String>
plugin_system_hook_agent_behavior(plugin_id: String, agent_id: String) -> Result<String, String>
plugin_system_register_ui_component(plugin_id: String, component_data: UIComponent) -> Result<String, String>
plugin_system_get_api_access(plugin_id: String, api_name: String) -> Result<ApiAccess, String>

// Plugin Configuration Commands
plugin_config_set(plugin_id: String, key: String, value: String) -> Result<String, String>
plugin_config_get(plugin_id: String, key: String) -> Result<String, String>
plugin_config_delete(plugin_id: String, key: String) -> Result<String, String>
plugin_config_list(plugin_id: String) -> Result<HashMap<String, String>, String>

// Plugin Event System Commands
plugin_events_subscribe(plugin_id: String, event_type: String) -> Result<String, String>
plugin_events_unsubscribe(plugin_id: String, event_type: String) -> Result<String, String>
plugin_events_emit(plugin_id: String, event_data: PluginEvent) -> Result<String, String>
plugin_events_get_history(plugin_id: String) -> Result<Vec<PluginEvent>, String>

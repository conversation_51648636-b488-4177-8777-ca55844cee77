use std::path::PathBuf;
use std::process::Command;
use serde::{Deserialize, Serialize};

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct NativePermissionRequest {
    pub app_name: String,
    pub requested_paths: Vec<PathBuf>,
    pub purpose: String,
    pub permission_type: PermissionType,
}

#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum PermissionType {
    ReadOnly,
    ReadWrite,
    FullAccess,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct PermissionResult {
    pub granted: bool,
    pub granted_paths: Vec<PathBuf>,
    pub user_cancelled: bool,
    pub error: Option<String>,
}

pub struct NativePermissionManager;

impl NativePermissionManager {
    pub fn new() -> Self {
        Self
    }

    /// Request native macOS permission using system dialogs
    pub async fn request_file_access(
        &self,
        app_name: &str,
        paths: &[PathBuf],
        purpose: &str,
    ) -> Result<PermissionResult, String> {
        // First, show our custom permission dialog explaining what we need
        let user_consent = self.show_permission_dialog(app_name, paths, purpose).await?;
        
        if !user_consent {
            return Ok(PermissionResult {
                granted: false,
                granted_paths: vec![],
                user_cancelled: true,
                error: None,
            });
        }

        // Then request actual file system access
        let mut granted_paths = Vec::new();
        
        for path in paths {
            match self.request_path_access(path).await {
                Ok(granted_path) => {
                    if let Some(path) = granted_path {
                        granted_paths.push(path);
                    }
                }
                Err(e) => {
                    eprintln!("Failed to get access to {:?}: {}", path, e);
                }
            }
        }

        Ok(PermissionResult {
            granted: !granted_paths.is_empty(),
            granted_paths,
            user_cancelled: false,
            error: None,
        })
    }

    /// Show a native macOS permission dialog
    async fn show_permission_dialog(
        &self,
        app_name: &str,
        paths: &[PathBuf],
        purpose: &str,
    ) -> Result<bool, String> {
        let path_list = paths
            .iter()
            .map(|p| format!("• {}", p.display()))
            .collect::<Vec<_>>()
            .join("\n");

        let message = format!(
            "{} needs access to the following locations to function:\n\n{}\n\nPurpose: {}\n\nClick 'Allow' to grant access, or 'Deny' to refuse.",
            app_name, path_list, purpose
        );

        // Use native macOS dialog via osascript
        let script = format!(
            r#"display dialog "{}" with title "Permission Request - {}" buttons {{"Deny", "Allow"}} default button "Allow" with icon caution"#,
            message.replace("\"", "\\\""), app_name
        );

        let output = Command::new("osascript")
            .arg("-e")
            .arg(&script)
            .output()
            .map_err(|e| format!("Failed to show dialog: {}", e))?;

        if output.status.success() {
            let result = String::from_utf8_lossy(&output.stdout);
            Ok(result.contains("Allow"))
        } else {
            // User cancelled or denied
            Ok(false)
        }
    }

    /// Request access to a specific path using macOS file picker
    async fn request_path_access(&self, path: &PathBuf) -> Result<Option<PathBuf>, String> {
        // Expand tilde in path
        let expanded_path = self.expand_path(path);
        
        // Check if path exists
        if !expanded_path.exists() {
            return Ok(None);
        }

        // For directories, we can directly check access
        if expanded_path.is_dir() {
            match std::fs::read_dir(&expanded_path) {
                Ok(_) => Ok(Some(expanded_path)),
                Err(_) => {
                    // If we can't read the directory, try to request permission
                    self.request_directory_permission(&expanded_path).await
                }
            }
        } else {
            // For files, check if we can read them
            match std::fs::read_to_string(&expanded_path) {
                Ok(_) => Ok(Some(expanded_path)),
                Err(_) => {
                    // If we can't read the file, try to request permission
                    self.request_file_permission(&expanded_path).await
                }
            }
        }
    }

    /// Request permission for a directory using native macOS file picker
    async fn request_directory_permission(&self, path: &PathBuf) -> Result<Option<PathBuf>, String> {
        println!("Requesting macOS permission for directory: {}", path.display());

        // First check if we already have access
        if self.check_access(path) {
            println!("✅ Already have access to: {}", path.display());
            return Ok(Some(path.clone()));
        }

        // Use macOS native file picker via osascript
        let script = format!(
            r#"POSIX path of (choose folder with prompt "Grant access to {}" default location "{}")"#,
            path.display(),
            path.display()
        );

        let output = Command::new("osascript")
            .arg("-e")
            .arg(&script)
            .output()
            .map_err(|e| format!("Failed to show file picker: {}", e))?;

        if output.status.success() {
            let selected_path_str = String::from_utf8_lossy(&output.stdout).trim().to_string();
            let selected_path = PathBuf::from(selected_path_str);

            println!("✅ User granted access to: {}", selected_path.display());

            // Return the originally requested path if user selected it or a parent
            if selected_path == *path || path.starts_with(&selected_path) {
                Ok(Some(path.clone()))
            } else {
                Ok(Some(selected_path))
            }
        } else {
            println!("❌ User denied access to: {}", path.display());
            Ok(None)
        }
    }

    /// Request permission for a file using native macOS dialogs
    async fn request_file_permission(&self, path: &PathBuf) -> Result<Option<PathBuf>, String> {
        // For files, we'll request access to the parent directory
        if let Some(parent) = path.parent() {
            self.request_directory_permission(&parent.to_path_buf()).await
        } else {
            Ok(None)
        }
    }

    /// Expand ~ in paths to full home directory path
    fn expand_path(&self, path: &PathBuf) -> PathBuf {
        if path.starts_with("~") {
            if let Some(home_dir) = dirs::home_dir() {
                home_dir.join(path.strip_prefix("~").unwrap_or(path))
            } else {
                path.clone()
            }
        } else {
            path.clone()
        }
    }

    /// Check if we have access to a path
    pub fn check_access(&self, path: &PathBuf) -> bool {
        let expanded_path = self.expand_path(path);
        
        if expanded_path.is_dir() {
            std::fs::read_dir(&expanded_path).is_ok()
        } else {
            std::fs::metadata(&expanded_path).is_ok()
        }
    }

    /// Read file content with permission check
    pub fn read_file_content(&self, path: &PathBuf) -> Result<String, String> {
        let expanded_path = self.expand_path(path);
        
        if !self.check_access(&expanded_path) {
            return Err("No permission to access this file".to_string());
        }

        std::fs::read_to_string(&expanded_path).map_err(|e| e.to_string())
    }

    /// List files in directory with permission check
    pub fn list_directory(&self, path: &PathBuf) -> Result<Vec<PathBuf>, String> {
        let expanded_path = self.expand_path(path);
        
        if !self.check_access(&expanded_path) {
            return Err("No permission to access this directory".to_string());
        }

        let mut files = Vec::new();
        let entries = std::fs::read_dir(&expanded_path).map_err(|e| e.to_string())?;
        
        for entry in entries {
            if let Ok(entry) = entry {
                files.push(entry.path());
            }
        }

        Ok(files)
    }
}

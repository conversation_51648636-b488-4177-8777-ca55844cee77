<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <!-- File system access -->
    <key>com.apple.security.files.user-selected.read-write</key>
    <true/>
    <key>com.apple.security.files.downloads.read-write</key>
    <true/>
    
    <!-- Documents folder access -->
    <key>com.apple.security.files.bookmarks.app-scope</key>
    <true/>
    
    <!-- Network access for potential future features -->
    <key>com.apple.security.network.client</key>
    <true/>
    
    <!-- Disable sandboxing for full file system access -->
    <key>com.apple.security.app-sandbox</key>
    <false/>
    
    <!-- Allow reading user's home directory -->
    <key>com.apple.security.temporary-exception.files.home-relative-path.read-write</key>
    <array>
        <string>Documents/</string>
        <string>Library/Application Support/</string>
        <string>Downloads/</string>
    </array>
</dict>
</plist>
